{"version": 3, "sources": ["../../re-resizable/lib/index.js", "../../re-resizable/lib/resizer.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { PureComponent } from 'react';\nimport { flushSync } from 'react-dom';\nimport { Resizer } from './resizer';\nvar DEFAULT_SIZE = {\n    width: 'auto',\n    height: 'auto',\n};\nvar clamp = function (n, min, max) { return Math.max(Math.min(n, max), min); };\nvar snap = function (n, size, gridGap) {\n    var v = Math.round(n / size);\n    return v * size + gridGap * (v - 1);\n};\nvar hasDirection = function (dir, target) {\n    return new RegExp(dir, 'i').test(target);\n};\n// INFO: In case of window is a Proxy and does not porxy Events correctly, use isTouchEvent & isMouseEvent to distinguish event type instead of `instanceof`.\nvar isTouchEvent = function (event) {\n    return Boolean(event.touches && event.touches.length);\n};\nvar isMouseEvent = function (event) {\n    return Boolean((event.clientX || event.clientX === 0) &&\n        (event.clientY || event.clientY === 0));\n};\nvar findClosestSnap = function (n, snapArray, snapGap) {\n    if (snapGap === void 0) { snapGap = 0; }\n    var closestGapIndex = snapArray.reduce(function (prev, curr, index) { return (Math.abs(curr - n) < Math.abs(snapArray[prev] - n) ? index : prev); }, 0);\n    var gap = Math.abs(snapArray[closestGapIndex] - n);\n    return snapGap === 0 || gap < snapGap ? snapArray[closestGapIndex] : n;\n};\nvar getStringSize = function (n) {\n    n = n.toString();\n    if (n === 'auto') {\n        return n;\n    }\n    if (n.endsWith('px')) {\n        return n;\n    }\n    if (n.endsWith('%')) {\n        return n;\n    }\n    if (n.endsWith('vh')) {\n        return n;\n    }\n    if (n.endsWith('vw')) {\n        return n;\n    }\n    if (n.endsWith('vmax')) {\n        return n;\n    }\n    if (n.endsWith('vmin')) {\n        return n;\n    }\n    return \"\".concat(n, \"px\");\n};\nvar getPixelSize = function (size, parentSize, innerWidth, innerHeight) {\n    if (size && typeof size === 'string') {\n        if (size.endsWith('px')) {\n            return Number(size.replace('px', ''));\n        }\n        if (size.endsWith('%')) {\n            var ratio = Number(size.replace('%', '')) / 100;\n            return parentSize * ratio;\n        }\n        if (size.endsWith('vw')) {\n            var ratio = Number(size.replace('vw', '')) / 100;\n            return innerWidth * ratio;\n        }\n        if (size.endsWith('vh')) {\n            var ratio = Number(size.replace('vh', '')) / 100;\n            return innerHeight * ratio;\n        }\n    }\n    return size;\n};\nvar calculateNewMax = function (parentSize, innerWidth, innerHeight, maxWidth, maxHeight, minWidth, minHeight) {\n    maxWidth = getPixelSize(maxWidth, parentSize.width, innerWidth, innerHeight);\n    maxHeight = getPixelSize(maxHeight, parentSize.height, innerWidth, innerHeight);\n    minWidth = getPixelSize(minWidth, parentSize.width, innerWidth, innerHeight);\n    minHeight = getPixelSize(minHeight, parentSize.height, innerWidth, innerHeight);\n    return {\n        maxWidth: typeof maxWidth === 'undefined' ? undefined : Number(maxWidth),\n        maxHeight: typeof maxHeight === 'undefined' ? undefined : Number(maxHeight),\n        minWidth: typeof minWidth === 'undefined' ? undefined : Number(minWidth),\n        minHeight: typeof minHeight === 'undefined' ? undefined : Number(minHeight),\n    };\n};\n/**\n * transform T | [T, T] to [T, T]\n * @param val\n * @returns\n */\n// tslint:disable-next-line\nvar normalizeToPair = function (val) { return (Array.isArray(val) ? val : [val, val]); };\nvar definedProps = [\n    'as',\n    'ref',\n    'style',\n    'className',\n    'grid',\n    'gridGap',\n    'snap',\n    'bounds',\n    'boundsByDirection',\n    'size',\n    'defaultSize',\n    'minWidth',\n    'minHeight',\n    'maxWidth',\n    'maxHeight',\n    'lockAspectRatio',\n    'lockAspectRatioExtraWidth',\n    'lockAspectRatioExtraHeight',\n    'enable',\n    'handleStyles',\n    'handleClasses',\n    'handleWrapperStyle',\n    'handleWrapperClass',\n    'children',\n    'onResizeStart',\n    'onResize',\n    'onResizeStop',\n    'handleComponent',\n    'scale',\n    'resizeRatio',\n    'snapGap',\n];\n// HACK: This class is used to calculate % size.\nvar baseClassName = '__resizable_base__';\nvar Resizable = /** @class */ (function (_super) {\n    __extends(Resizable, _super);\n    function Resizable(props) {\n        var _a, _b, _c, _d;\n        var _this = _super.call(this, props) || this;\n        _this.ratio = 1;\n        _this.resizable = null;\n        // For parent boundary\n        _this.parentLeft = 0;\n        _this.parentTop = 0;\n        // For boundary\n        _this.resizableLeft = 0;\n        _this.resizableRight = 0;\n        _this.resizableTop = 0;\n        _this.resizableBottom = 0;\n        // For target boundary\n        _this.targetLeft = 0;\n        _this.targetTop = 0;\n        _this.delta = {\n            width: 0,\n            height: 0,\n        };\n        _this.appendBase = function () {\n            if (!_this.resizable || !_this.window) {\n                return null;\n            }\n            var parent = _this.parentNode;\n            if (!parent) {\n                return null;\n            }\n            var element = _this.window.document.createElement('div');\n            element.style.width = '100%';\n            element.style.height = '100%';\n            element.style.position = 'absolute';\n            element.style.transform = 'scale(0, 0)';\n            element.style.left = '0';\n            element.style.flex = '0 0 100%';\n            if (element.classList) {\n                element.classList.add(baseClassName);\n            }\n            else {\n                element.className += baseClassName;\n            }\n            parent.appendChild(element);\n            return element;\n        };\n        _this.removeBase = function (base) {\n            var parent = _this.parentNode;\n            if (!parent) {\n                return;\n            }\n            parent.removeChild(base);\n        };\n        _this.state = {\n            isResizing: false,\n            width: (_b = (_a = _this.propsSize) === null || _a === void 0 ? void 0 : _a.width) !== null && _b !== void 0 ? _b : 'auto',\n            height: (_d = (_c = _this.propsSize) === null || _c === void 0 ? void 0 : _c.height) !== null && _d !== void 0 ? _d : 'auto',\n            direction: 'right',\n            original: {\n                x: 0,\n                y: 0,\n                width: 0,\n                height: 0,\n            },\n            backgroundStyle: {\n                height: '100%',\n                width: '100%',\n                backgroundColor: 'rgba(0,0,0,0)',\n                cursor: 'auto',\n                opacity: 0,\n                position: 'fixed',\n                zIndex: 9999,\n                top: '0',\n                left: '0',\n                bottom: '0',\n                right: '0',\n            },\n            flexBasis: undefined,\n        };\n        _this.onResizeStart = _this.onResizeStart.bind(_this);\n        _this.onMouseMove = _this.onMouseMove.bind(_this);\n        _this.onMouseUp = _this.onMouseUp.bind(_this);\n        return _this;\n    }\n    Object.defineProperty(Resizable.prototype, \"parentNode\", {\n        get: function () {\n            if (!this.resizable) {\n                return null;\n            }\n            return this.resizable.parentNode;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Resizable.prototype, \"window\", {\n        get: function () {\n            if (!this.resizable) {\n                return null;\n            }\n            if (!this.resizable.ownerDocument) {\n                return null;\n            }\n            return this.resizable.ownerDocument.defaultView;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Resizable.prototype, \"propsSize\", {\n        get: function () {\n            return this.props.size || this.props.defaultSize || DEFAULT_SIZE;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Resizable.prototype, \"size\", {\n        get: function () {\n            var width = 0;\n            var height = 0;\n            if (this.resizable && this.window) {\n                var orgWidth = this.resizable.offsetWidth;\n                var orgHeight = this.resizable.offsetHeight;\n                // HACK: Set position `relative` to get parent size.\n                //       This is because when re-resizable set `absolute`, I can not get base width correctly.\n                var orgPosition = this.resizable.style.position;\n                if (orgPosition !== 'relative') {\n                    this.resizable.style.position = 'relative';\n                }\n                // INFO: Use original width or height if set auto.\n                width = this.resizable.style.width !== 'auto' ? this.resizable.offsetWidth : orgWidth;\n                height = this.resizable.style.height !== 'auto' ? this.resizable.offsetHeight : orgHeight;\n                // Restore original position\n                this.resizable.style.position = orgPosition;\n            }\n            return { width: width, height: height };\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Resizable.prototype, \"sizeStyle\", {\n        get: function () {\n            var _this = this;\n            var size = this.props.size;\n            var getSize = function (key) {\n                var _a;\n                if (typeof _this.state[key] === 'undefined' || _this.state[key] === 'auto') {\n                    return 'auto';\n                }\n                if (_this.propsSize && _this.propsSize[key] && ((_a = _this.propsSize[key]) === null || _a === void 0 ? void 0 : _a.toString().endsWith('%'))) {\n                    if (_this.state[key].toString().endsWith('%')) {\n                        return _this.state[key].toString();\n                    }\n                    var parentSize = _this.getParentSize();\n                    var value = Number(_this.state[key].toString().replace('px', ''));\n                    var percent = (value / parentSize[key]) * 100;\n                    return \"\".concat(percent, \"%\");\n                }\n                return getStringSize(_this.state[key]);\n            };\n            var width = size && typeof size.width !== 'undefined' && !this.state.isResizing\n                ? getStringSize(size.width)\n                : getSize('width');\n            var height = size && typeof size.height !== 'undefined' && !this.state.isResizing\n                ? getStringSize(size.height)\n                : getSize('height');\n            return { width: width, height: height };\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Resizable.prototype.getParentSize = function () {\n        if (!this.parentNode) {\n            if (!this.window) {\n                return { width: 0, height: 0 };\n            }\n            return { width: this.window.innerWidth, height: this.window.innerHeight };\n        }\n        var base = this.appendBase();\n        if (!base) {\n            return { width: 0, height: 0 };\n        }\n        // INFO: To calculate parent width with flex layout\n        var wrapChanged = false;\n        var wrap = this.parentNode.style.flexWrap;\n        if (wrap !== 'wrap') {\n            wrapChanged = true;\n            this.parentNode.style.flexWrap = 'wrap';\n            // HACK: Use relative to get parent padding size\n        }\n        base.style.position = 'relative';\n        base.style.minWidth = '100%';\n        base.style.minHeight = '100%';\n        var size = {\n            width: base.offsetWidth,\n            height: base.offsetHeight,\n        };\n        if (wrapChanged) {\n            this.parentNode.style.flexWrap = wrap;\n        }\n        this.removeBase(base);\n        return size;\n    };\n    Resizable.prototype.bindEvents = function () {\n        if (this.window) {\n            this.window.addEventListener('mouseup', this.onMouseUp);\n            this.window.addEventListener('mousemove', this.onMouseMove);\n            this.window.addEventListener('mouseleave', this.onMouseUp);\n            this.window.addEventListener('touchmove', this.onMouseMove, {\n                capture: true,\n                passive: false,\n            });\n            this.window.addEventListener('touchend', this.onMouseUp);\n        }\n    };\n    Resizable.prototype.unbindEvents = function () {\n        if (this.window) {\n            this.window.removeEventListener('mouseup', this.onMouseUp);\n            this.window.removeEventListener('mousemove', this.onMouseMove);\n            this.window.removeEventListener('mouseleave', this.onMouseUp);\n            this.window.removeEventListener('touchmove', this.onMouseMove, true);\n            this.window.removeEventListener('touchend', this.onMouseUp);\n        }\n    };\n    Resizable.prototype.componentDidMount = function () {\n        if (!this.resizable || !this.window) {\n            return;\n        }\n        var computedStyle = this.window.getComputedStyle(this.resizable);\n        this.setState({\n            width: this.state.width || this.size.width,\n            height: this.state.height || this.size.height,\n            flexBasis: computedStyle.flexBasis !== 'auto' ? computedStyle.flexBasis : undefined,\n        });\n    };\n    Resizable.prototype.componentWillUnmount = function () {\n        if (this.window) {\n            this.unbindEvents();\n        }\n    };\n    Resizable.prototype.createSizeForCssProperty = function (newSize, kind) {\n        var propsSize = this.propsSize && this.propsSize[kind];\n        return this.state[kind] === 'auto' &&\n            this.state.original[kind] === newSize &&\n            (typeof propsSize === 'undefined' || propsSize === 'auto')\n            ? 'auto'\n            : newSize;\n    };\n    Resizable.prototype.calculateNewMaxFromBoundary = function (maxWidth, maxHeight) {\n        var boundsByDirection = this.props.boundsByDirection;\n        var direction = this.state.direction;\n        var widthByDirection = boundsByDirection && hasDirection('left', direction);\n        var heightByDirection = boundsByDirection && hasDirection('top', direction);\n        var boundWidth;\n        var boundHeight;\n        if (this.props.bounds === 'parent') {\n            var parent_1 = this.parentNode;\n            if (parent_1) {\n                boundWidth = widthByDirection\n                    ? this.resizableRight - this.parentLeft\n                    : parent_1.offsetWidth + (this.parentLeft - this.resizableLeft);\n                boundHeight = heightByDirection\n                    ? this.resizableBottom - this.parentTop\n                    : parent_1.offsetHeight + (this.parentTop - this.resizableTop);\n            }\n        }\n        else if (this.props.bounds === 'window') {\n            if (this.window) {\n                boundWidth = widthByDirection ? this.resizableRight : this.window.innerWidth - this.resizableLeft;\n                boundHeight = heightByDirection ? this.resizableBottom : this.window.innerHeight - this.resizableTop;\n            }\n        }\n        else if (this.props.bounds) {\n            boundWidth = widthByDirection\n                ? this.resizableRight - this.targetLeft\n                : this.props.bounds.offsetWidth + (this.targetLeft - this.resizableLeft);\n            boundHeight = heightByDirection\n                ? this.resizableBottom - this.targetTop\n                : this.props.bounds.offsetHeight + (this.targetTop - this.resizableTop);\n        }\n        if (boundWidth && Number.isFinite(boundWidth)) {\n            maxWidth = maxWidth && maxWidth < boundWidth ? maxWidth : boundWidth;\n        }\n        if (boundHeight && Number.isFinite(boundHeight)) {\n            maxHeight = maxHeight && maxHeight < boundHeight ? maxHeight : boundHeight;\n        }\n        return { maxWidth: maxWidth, maxHeight: maxHeight };\n    };\n    Resizable.prototype.calculateNewSizeFromDirection = function (clientX, clientY) {\n        var scale = this.props.scale || 1;\n        var _a = normalizeToPair(this.props.resizeRatio || 1), resizeRatioX = _a[0], resizeRatioY = _a[1];\n        var _b = this.state, direction = _b.direction, original = _b.original;\n        var _c = this.props, lockAspectRatio = _c.lockAspectRatio, lockAspectRatioExtraHeight = _c.lockAspectRatioExtraHeight, lockAspectRatioExtraWidth = _c.lockAspectRatioExtraWidth;\n        var newWidth = original.width;\n        var newHeight = original.height;\n        var extraHeight = lockAspectRatioExtraHeight || 0;\n        var extraWidth = lockAspectRatioExtraWidth || 0;\n        if (hasDirection('right', direction)) {\n            newWidth = original.width + ((clientX - original.x) * resizeRatioX) / scale;\n            if (lockAspectRatio) {\n                newHeight = (newWidth - extraWidth) / this.ratio + extraHeight;\n            }\n        }\n        if (hasDirection('left', direction)) {\n            newWidth = original.width - ((clientX - original.x) * resizeRatioX) / scale;\n            if (lockAspectRatio) {\n                newHeight = (newWidth - extraWidth) / this.ratio + extraHeight;\n            }\n        }\n        if (hasDirection('bottom', direction)) {\n            newHeight = original.height + ((clientY - original.y) * resizeRatioY) / scale;\n            if (lockAspectRatio) {\n                newWidth = (newHeight - extraHeight) * this.ratio + extraWidth;\n            }\n        }\n        if (hasDirection('top', direction)) {\n            newHeight = original.height - ((clientY - original.y) * resizeRatioY) / scale;\n            if (lockAspectRatio) {\n                newWidth = (newHeight - extraHeight) * this.ratio + extraWidth;\n            }\n        }\n        return { newWidth: newWidth, newHeight: newHeight };\n    };\n    Resizable.prototype.calculateNewSizeFromAspectRatio = function (newWidth, newHeight, max, min) {\n        var _a = this.props, lockAspectRatio = _a.lockAspectRatio, lockAspectRatioExtraHeight = _a.lockAspectRatioExtraHeight, lockAspectRatioExtraWidth = _a.lockAspectRatioExtraWidth;\n        var computedMinWidth = typeof min.width === 'undefined' ? 10 : min.width;\n        var computedMaxWidth = typeof max.width === 'undefined' || max.width < 0 ? newWidth : max.width;\n        var computedMinHeight = typeof min.height === 'undefined' ? 10 : min.height;\n        var computedMaxHeight = typeof max.height === 'undefined' || max.height < 0 ? newHeight : max.height;\n        var extraHeight = lockAspectRatioExtraHeight || 0;\n        var extraWidth = lockAspectRatioExtraWidth || 0;\n        if (lockAspectRatio) {\n            var extraMinWidth = (computedMinHeight - extraHeight) * this.ratio + extraWidth;\n            var extraMaxWidth = (computedMaxHeight - extraHeight) * this.ratio + extraWidth;\n            var extraMinHeight = (computedMinWidth - extraWidth) / this.ratio + extraHeight;\n            var extraMaxHeight = (computedMaxWidth - extraWidth) / this.ratio + extraHeight;\n            var lockedMinWidth = Math.max(computedMinWidth, extraMinWidth);\n            var lockedMaxWidth = Math.min(computedMaxWidth, extraMaxWidth);\n            var lockedMinHeight = Math.max(computedMinHeight, extraMinHeight);\n            var lockedMaxHeight = Math.min(computedMaxHeight, extraMaxHeight);\n            newWidth = clamp(newWidth, lockedMinWidth, lockedMaxWidth);\n            newHeight = clamp(newHeight, lockedMinHeight, lockedMaxHeight);\n        }\n        else {\n            newWidth = clamp(newWidth, computedMinWidth, computedMaxWidth);\n            newHeight = clamp(newHeight, computedMinHeight, computedMaxHeight);\n        }\n        return { newWidth: newWidth, newHeight: newHeight };\n    };\n    Resizable.prototype.setBoundingClientRect = function () {\n        var adjustedScale = 1 / (this.props.scale || 1);\n        // For parent boundary\n        if (this.props.bounds === 'parent') {\n            var parent_2 = this.parentNode;\n            if (parent_2) {\n                var parentRect = parent_2.getBoundingClientRect();\n                this.parentLeft = parentRect.left * adjustedScale;\n                this.parentTop = parentRect.top * adjustedScale;\n            }\n        }\n        // For target(html element) boundary\n        if (this.props.bounds && typeof this.props.bounds !== 'string') {\n            var targetRect = this.props.bounds.getBoundingClientRect();\n            this.targetLeft = targetRect.left * adjustedScale;\n            this.targetTop = targetRect.top * adjustedScale;\n        }\n        // For boundary\n        if (this.resizable) {\n            var _a = this.resizable.getBoundingClientRect(), left = _a.left, top_1 = _a.top, right = _a.right, bottom = _a.bottom;\n            this.resizableLeft = left * adjustedScale;\n            this.resizableRight = right * adjustedScale;\n            this.resizableTop = top_1 * adjustedScale;\n            this.resizableBottom = bottom * adjustedScale;\n        }\n    };\n    Resizable.prototype.onResizeStart = function (event, direction) {\n        if (!this.resizable || !this.window) {\n            return;\n        }\n        var clientX = 0;\n        var clientY = 0;\n        if (event.nativeEvent && isMouseEvent(event.nativeEvent)) {\n            clientX = event.nativeEvent.clientX;\n            clientY = event.nativeEvent.clientY;\n        }\n        else if (event.nativeEvent && isTouchEvent(event.nativeEvent)) {\n            clientX = event.nativeEvent.touches[0].clientX;\n            clientY = event.nativeEvent.touches[0].clientY;\n        }\n        if (this.props.onResizeStart) {\n            if (this.resizable) {\n                var startResize = this.props.onResizeStart(event, direction, this.resizable);\n                if (startResize === false) {\n                    return;\n                }\n            }\n        }\n        // Fix #168\n        if (this.props.size) {\n            if (typeof this.props.size.height !== 'undefined' && this.props.size.height !== this.state.height) {\n                this.setState({ height: this.props.size.height });\n            }\n            if (typeof this.props.size.width !== 'undefined' && this.props.size.width !== this.state.width) {\n                this.setState({ width: this.props.size.width });\n            }\n        }\n        // For lockAspectRatio case\n        this.ratio =\n            typeof this.props.lockAspectRatio === 'number' ? this.props.lockAspectRatio : this.size.width / this.size.height;\n        var flexBasis;\n        var computedStyle = this.window.getComputedStyle(this.resizable);\n        if (computedStyle.flexBasis !== 'auto') {\n            var parent_3 = this.parentNode;\n            if (parent_3) {\n                var dir = this.window.getComputedStyle(parent_3).flexDirection;\n                this.flexDir = dir.startsWith('row') ? 'row' : 'column';\n                flexBasis = computedStyle.flexBasis;\n            }\n        }\n        // For boundary\n        this.setBoundingClientRect();\n        this.bindEvents();\n        var state = {\n            original: {\n                x: clientX,\n                y: clientY,\n                width: this.size.width,\n                height: this.size.height,\n            },\n            isResizing: true,\n            backgroundStyle: __assign(__assign({}, this.state.backgroundStyle), { cursor: this.window.getComputedStyle(event.target).cursor || 'auto' }),\n            direction: direction,\n            flexBasis: flexBasis,\n        };\n        this.setState(state);\n    };\n    Resizable.prototype.onMouseMove = function (event) {\n        var _this = this;\n        if (!this.state.isResizing || !this.resizable || !this.window) {\n            return;\n        }\n        if (this.window.TouchEvent && isTouchEvent(event)) {\n            try {\n                event.preventDefault();\n                event.stopPropagation();\n            }\n            catch (e) {\n                // Ignore on fail\n            }\n        }\n        var _a = this.props, maxWidth = _a.maxWidth, maxHeight = _a.maxHeight, minWidth = _a.minWidth, minHeight = _a.minHeight;\n        var clientX = isTouchEvent(event) ? event.touches[0].clientX : event.clientX;\n        var clientY = isTouchEvent(event) ? event.touches[0].clientY : event.clientY;\n        var _b = this.state, direction = _b.direction, original = _b.original, width = _b.width, height = _b.height;\n        var parentSize = this.getParentSize();\n        var max = calculateNewMax(parentSize, this.window.innerWidth, this.window.innerHeight, maxWidth, maxHeight, minWidth, minHeight);\n        maxWidth = max.maxWidth;\n        maxHeight = max.maxHeight;\n        minWidth = max.minWidth;\n        minHeight = max.minHeight;\n        // Calculate new size\n        var _c = this.calculateNewSizeFromDirection(clientX, clientY), newHeight = _c.newHeight, newWidth = _c.newWidth;\n        // Calculate max size from boundary settings\n        var boundaryMax = this.calculateNewMaxFromBoundary(maxWidth, maxHeight);\n        if (this.props.snap && this.props.snap.x) {\n            newWidth = findClosestSnap(newWidth, this.props.snap.x, this.props.snapGap);\n        }\n        if (this.props.snap && this.props.snap.y) {\n            newHeight = findClosestSnap(newHeight, this.props.snap.y, this.props.snapGap);\n        }\n        // Calculate new size from aspect ratio\n        var newSize = this.calculateNewSizeFromAspectRatio(newWidth, newHeight, { width: boundaryMax.maxWidth, height: boundaryMax.maxHeight }, { width: minWidth, height: minHeight });\n        newWidth = newSize.newWidth;\n        newHeight = newSize.newHeight;\n        if (this.props.grid) {\n            var newGridWidth = snap(newWidth, this.props.grid[0], this.props.gridGap ? this.props.gridGap[0] : 0);\n            var newGridHeight = snap(newHeight, this.props.grid[1], this.props.gridGap ? this.props.gridGap[1] : 0);\n            var gap = this.props.snapGap || 0;\n            var w = gap === 0 || Math.abs(newGridWidth - newWidth) <= gap ? newGridWidth : newWidth;\n            var h = gap === 0 || Math.abs(newGridHeight - newHeight) <= gap ? newGridHeight : newHeight;\n            newWidth = w;\n            newHeight = h;\n        }\n        var delta = {\n            width: newWidth - original.width,\n            height: newHeight - original.height,\n        };\n        this.delta = delta;\n        if (width && typeof width === 'string') {\n            if (width.endsWith('%')) {\n                var percent = (newWidth / parentSize.width) * 100;\n                newWidth = \"\".concat(percent, \"%\");\n            }\n            else if (width.endsWith('vw')) {\n                var vw = (newWidth / this.window.innerWidth) * 100;\n                newWidth = \"\".concat(vw, \"vw\");\n            }\n            else if (width.endsWith('vh')) {\n                var vh = (newWidth / this.window.innerHeight) * 100;\n                newWidth = \"\".concat(vh, \"vh\");\n            }\n        }\n        if (height && typeof height === 'string') {\n            if (height.endsWith('%')) {\n                var percent = (newHeight / parentSize.height) * 100;\n                newHeight = \"\".concat(percent, \"%\");\n            }\n            else if (height.endsWith('vw')) {\n                var vw = (newHeight / this.window.innerWidth) * 100;\n                newHeight = \"\".concat(vw, \"vw\");\n            }\n            else if (height.endsWith('vh')) {\n                var vh = (newHeight / this.window.innerHeight) * 100;\n                newHeight = \"\".concat(vh, \"vh\");\n            }\n        }\n        var newState = {\n            width: this.createSizeForCssProperty(newWidth, 'width'),\n            height: this.createSizeForCssProperty(newHeight, 'height'),\n        };\n        if (this.flexDir === 'row') {\n            newState.flexBasis = newState.width;\n        }\n        else if (this.flexDir === 'column') {\n            newState.flexBasis = newState.height;\n        }\n        var widthChanged = this.state.width !== newState.width;\n        var heightChanged = this.state.height !== newState.height;\n        var flexBaseChanged = this.state.flexBasis !== newState.flexBasis;\n        var changed = widthChanged || heightChanged || flexBaseChanged;\n        if (changed) {\n            // For v18, update state sync\n            flushSync(function () {\n                _this.setState(newState);\n            });\n        }\n        if (this.props.onResize) {\n            if (changed) {\n                this.props.onResize(event, direction, this.resizable, delta);\n            }\n        }\n    };\n    Resizable.prototype.onMouseUp = function (event) {\n        var _a, _b;\n        var _c = this.state, isResizing = _c.isResizing, direction = _c.direction, original = _c.original;\n        if (!isResizing || !this.resizable) {\n            return;\n        }\n        if (this.props.onResizeStop) {\n            this.props.onResizeStop(event, direction, this.resizable, this.delta);\n        }\n        if (this.props.size) {\n            this.setState({ width: (_a = this.props.size.width) !== null && _a !== void 0 ? _a : 'auto', height: (_b = this.props.size.height) !== null && _b !== void 0 ? _b : 'auto' });\n        }\n        this.unbindEvents();\n        this.setState({\n            isResizing: false,\n            backgroundStyle: __assign(__assign({}, this.state.backgroundStyle), { cursor: 'auto' }),\n        });\n    };\n    Resizable.prototype.updateSize = function (size) {\n        var _a, _b;\n        this.setState({ width: (_a = size.width) !== null && _a !== void 0 ? _a : 'auto', height: (_b = size.height) !== null && _b !== void 0 ? _b : 'auto' });\n    };\n    Resizable.prototype.renderResizer = function () {\n        var _this = this;\n        var _a = this.props, enable = _a.enable, handleStyles = _a.handleStyles, handleClasses = _a.handleClasses, handleWrapperStyle = _a.handleWrapperStyle, handleWrapperClass = _a.handleWrapperClass, handleComponent = _a.handleComponent;\n        if (!enable) {\n            return null;\n        }\n        var resizers = Object.keys(enable).map(function (dir) {\n            if (enable[dir] !== false) {\n                return (_jsx(Resizer, { direction: dir, onResizeStart: _this.onResizeStart, replaceStyles: handleStyles && handleStyles[dir], className: handleClasses && handleClasses[dir], children: handleComponent && handleComponent[dir] ? handleComponent[dir] : null }, dir));\n            }\n            return null;\n        });\n        // #93 Wrap the resize box in span (will not break 100% width/height)\n        return (_jsx(\"div\", { className: handleWrapperClass, style: handleWrapperStyle, children: resizers }));\n    };\n    Resizable.prototype.render = function () {\n        var _this = this;\n        var extendsProps = Object.keys(this.props).reduce(function (acc, key) {\n            if (definedProps.indexOf(key) !== -1) {\n                return acc;\n            }\n            acc[key] = _this.props[key];\n            return acc;\n        }, {});\n        var style = __assign(__assign(__assign({ position: 'relative', userSelect: this.state.isResizing ? 'none' : 'auto' }, this.props.style), this.sizeStyle), { maxWidth: this.props.maxWidth, maxHeight: this.props.maxHeight, minWidth: this.props.minWidth, minHeight: this.props.minHeight, boxSizing: 'border-box', flexShrink: 0 });\n        if (this.state.flexBasis) {\n            style.flexBasis = this.state.flexBasis;\n        }\n        var Wrapper = this.props.as || 'div';\n        return (_jsxs(Wrapper, __assign({ style: style, className: this.props.className }, extendsProps, { \n            // `ref` is after `extendsProps` to ensure this one wins over a version\n            // passed in\n            ref: function (c) {\n                if (c) {\n                    _this.resizable = c;\n                }\n            }, children: [this.state.isResizing && _jsx(\"div\", { style: this.state.backgroundStyle }), this.props.children, this.renderResizer()] })));\n    };\n    Resizable.defaultProps = {\n        as: 'div',\n        onResizeStart: function () { },\n        onResize: function () { },\n        onResizeStop: function () { },\n        enable: {\n            top: true,\n            right: true,\n            bottom: true,\n            left: true,\n            topRight: true,\n            bottomRight: true,\n            bottomLeft: true,\n            topLeft: true,\n        },\n        style: {},\n        grid: [1, 1],\n        gridGap: [0, 0],\n        lockAspectRatio: false,\n        lockAspectRatioExtraWidth: 0,\n        lockAspectRatioExtraHeight: 0,\n        scale: 1,\n        resizeRatio: 1,\n        snapGap: 0,\n    };\n    return Resizable;\n}(PureComponent));\nexport { Resizable };\n", "var __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { memo, useCallback, useMemo } from 'react';\nvar rowSizeBase = {\n    width: '100%',\n    height: '10px',\n    top: '0px',\n    left: '0px',\n    cursor: 'row-resize',\n};\nvar colSizeBase = {\n    width: '10px',\n    height: '100%',\n    top: '0px',\n    left: '0px',\n    cursor: 'col-resize',\n};\nvar edgeBase = {\n    width: '20px',\n    height: '20px',\n    position: 'absolute',\n    zIndex: 1,\n};\nvar styles = {\n    top: __assign(__assign({}, rowSizeBase), { top: '-5px' }),\n    right: __assign(__assign({}, colSizeBase), { left: undefined, right: '-5px' }),\n    bottom: __assign(__assign({}, rowSizeBase), { top: undefined, bottom: '-5px' }),\n    left: __assign(__assign({}, colSizeBase), { left: '-5px' }),\n    topRight: __assign(__assign({}, edgeBase), { right: '-10px', top: '-10px', cursor: 'ne-resize' }),\n    bottomRight: __assign(__assign({}, edgeBase), { right: '-10px', bottom: '-10px', cursor: 'se-resize' }),\n    bottomLeft: __assign(__assign({}, edgeBase), { left: '-10px', bottom: '-10px', cursor: 'sw-resize' }),\n    topLeft: __assign(__assign({}, edgeBase), { left: '-10px', top: '-10px', cursor: 'nw-resize' }),\n};\nexport var Resizer = memo(function (props) {\n    var onResizeStart = props.onResizeStart, direction = props.direction, children = props.children, replaceStyles = props.replaceStyles, className = props.className;\n    var onMouseDown = useCallback(function (e) {\n        onResizeStart(e, direction);\n    }, [onResizeStart, direction]);\n    var onTouchStart = useCallback(function (e) {\n        onResizeStart(e, direction);\n    }, [onResizeStart, direction]);\n    var style = useMemo(function () {\n        return __assign(__assign({ position: 'absolute', userSelect: 'none' }, styles[direction]), (replaceStyles !== null && replaceStyles !== void 0 ? replaceStyles : {}));\n    }, [replaceStyles, direction]);\n    return (_jsx(\"div\", { className: className || undefined, style: style, onMouseDown: onMouseDown, onTouchStart: onTouchStart, children: children }));\n});\n"], "mappings": ";;;;;;;;;;;;;;AA0BA,IAAAA,sBAA2C;AAC3C,IAAAC,gBAA8B;AAC9B,uBAA0B;;;ACjB1B,yBAA4B;AAC5B,mBAA2C;AAZ3C,IAAI,WAAsC,WAAY;AAClD,aAAW,OAAO,UAAU,SAAS,GAAG;AACpC,aAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,UAAI,UAAU,CAAC;AACf,eAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC;AAC1D,UAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IAClB;AACA,WAAO;AAAA,EACX;AACA,SAAO,SAAS,MAAM,MAAM,SAAS;AACzC;AAGA,IAAI,cAAc;AAAA,EACd,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,KAAK;AAAA,EACL,MAAM;AAAA,EACN,QAAQ;AACZ;AACA,IAAI,cAAc;AAAA,EACd,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,KAAK;AAAA,EACL,MAAM;AAAA,EACN,QAAQ;AACZ;AACA,IAAI,WAAW;AAAA,EACX,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,QAAQ;AACZ;AACA,IAAI,SAAS;AAAA,EACT,KAAK,SAAS,SAAS,CAAC,GAAG,WAAW,GAAG,EAAE,KAAK,OAAO,CAAC;AAAA,EACxD,OAAO,SAAS,SAAS,CAAC,GAAG,WAAW,GAAG,EAAE,MAAM,QAAW,OAAO,OAAO,CAAC;AAAA,EAC7E,QAAQ,SAAS,SAAS,CAAC,GAAG,WAAW,GAAG,EAAE,KAAK,QAAW,QAAQ,OAAO,CAAC;AAAA,EAC9E,MAAM,SAAS,SAAS,CAAC,GAAG,WAAW,GAAG,EAAE,MAAM,OAAO,CAAC;AAAA,EAC1D,UAAU,SAAS,SAAS,CAAC,GAAG,QAAQ,GAAG,EAAE,OAAO,SAAS,KAAK,SAAS,QAAQ,YAAY,CAAC;AAAA,EAChG,aAAa,SAAS,SAAS,CAAC,GAAG,QAAQ,GAAG,EAAE,OAAO,SAAS,QAAQ,SAAS,QAAQ,YAAY,CAAC;AAAA,EACtG,YAAY,SAAS,SAAS,CAAC,GAAG,QAAQ,GAAG,EAAE,MAAM,SAAS,QAAQ,SAAS,QAAQ,YAAY,CAAC;AAAA,EACpG,SAAS,SAAS,SAAS,CAAC,GAAG,QAAQ,GAAG,EAAE,MAAM,SAAS,KAAK,SAAS,QAAQ,YAAY,CAAC;AAClG;AACO,IAAI,cAAU,mBAAK,SAAU,OAAO;AACvC,MAAI,gBAAgB,MAAM,eAAe,YAAY,MAAM,WAAW,WAAW,MAAM,UAAU,gBAAgB,MAAM,eAAe,YAAY,MAAM;AACxJ,MAAI,kBAAc,0BAAY,SAAU,GAAG;AACvC,kBAAc,GAAG,SAAS;AAAA,EAC9B,GAAG,CAAC,eAAe,SAAS,CAAC;AAC7B,MAAI,mBAAe,0BAAY,SAAU,GAAG;AACxC,kBAAc,GAAG,SAAS;AAAA,EAC9B,GAAG,CAAC,eAAe,SAAS,CAAC;AAC7B,MAAI,YAAQ,sBAAQ,WAAY;AAC5B,WAAO,SAAS,SAAS,EAAE,UAAU,YAAY,YAAY,OAAO,GAAG,OAAO,SAAS,CAAC,GAAI,kBAAkB,QAAQ,kBAAkB,SAAS,gBAAgB,CAAC,CAAE;AAAA,EACxK,GAAG,CAAC,eAAe,SAAS,CAAC;AAC7B,aAAQ,mBAAAC,KAAK,OAAO,EAAE,WAAW,aAAa,QAAW,OAAc,aAA0B,cAA4B,SAAmB,CAAC;AACrJ,CAAC;;;ADvDD,IAAI,YAAyC,2BAAY;AACrD,MAAI,gBAAgB,SAAU,GAAG,GAAG;AAChC,oBAAgB,OAAO,kBAClB,EAAE,WAAW,CAAC,EAAE,aAAa,SAAS,SAAUC,IAAGC,IAAG;AAAE,MAAAD,GAAE,YAAYC;AAAA,IAAG,KAC1E,SAAUD,IAAGC,IAAG;AAAE,eAAS,KAAKA,GAAG,KAAI,OAAO,UAAU,eAAe,KAAKA,IAAG,CAAC,EAAG,CAAAD,GAAE,CAAC,IAAIC,GAAE,CAAC;AAAA,IAAG;AACpG,WAAO,cAAc,GAAG,CAAC;AAAA,EAC7B;AACA,SAAO,SAAU,GAAG,GAAG;AACnB,QAAI,OAAO,MAAM,cAAc,MAAM;AACjC,YAAM,IAAI,UAAU,yBAAyB,OAAO,CAAC,IAAI,+BAA+B;AAC5F,kBAAc,GAAG,CAAC;AAClB,aAAS,KAAK;AAAE,WAAK,cAAc;AAAA,IAAG;AACtC,MAAE,YAAY,MAAM,OAAO,OAAO,OAAO,CAAC,KAAK,GAAG,YAAY,EAAE,WAAW,IAAI,GAAG;AAAA,EACtF;AACJ,EAAG;AACH,IAAIC,YAAsC,WAAY;AAClD,EAAAA,YAAW,OAAO,UAAU,SAAS,GAAG;AACpC,aAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,UAAI,UAAU,CAAC;AACf,eAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC;AAC1D,UAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IAClB;AACA,WAAO;AAAA,EACX;AACA,SAAOA,UAAS,MAAM,MAAM,SAAS;AACzC;AAKA,IAAI,eAAe;AAAA,EACf,OAAO;AAAA,EACP,QAAQ;AACZ;AACA,IAAI,QAAQ,SAAU,GAAG,KAAK,KAAK;AAAE,SAAO,KAAK,IAAI,KAAK,IAAI,GAAG,GAAG,GAAG,GAAG;AAAG;AAC7E,IAAI,OAAO,SAAU,GAAG,MAAM,SAAS;AACnC,MAAI,IAAI,KAAK,MAAM,IAAI,IAAI;AAC3B,SAAO,IAAI,OAAO,WAAW,IAAI;AACrC;AACA,IAAI,eAAe,SAAU,KAAK,QAAQ;AACtC,SAAO,IAAI,OAAO,KAAK,GAAG,EAAE,KAAK,MAAM;AAC3C;AAEA,IAAI,eAAe,SAAU,OAAO;AAChC,SAAO,QAAQ,MAAM,WAAW,MAAM,QAAQ,MAAM;AACxD;AACA,IAAI,eAAe,SAAU,OAAO;AAChC,SAAO,SAAS,MAAM,WAAW,MAAM,YAAY,OAC9C,MAAM,WAAW,MAAM,YAAY,EAAE;AAC9C;AACA,IAAI,kBAAkB,SAAU,GAAG,WAAW,SAAS;AACnD,MAAI,YAAY,QAAQ;AAAE,cAAU;AAAA,EAAG;AACvC,MAAI,kBAAkB,UAAU,OAAO,SAAU,MAAM,MAAM,OAAO;AAAE,WAAQ,KAAK,IAAI,OAAO,CAAC,IAAI,KAAK,IAAI,UAAU,IAAI,IAAI,CAAC,IAAI,QAAQ;AAAA,EAAO,GAAG,CAAC;AACtJ,MAAI,MAAM,KAAK,IAAI,UAAU,eAAe,IAAI,CAAC;AACjD,SAAO,YAAY,KAAK,MAAM,UAAU,UAAU,eAAe,IAAI;AACzE;AACA,IAAI,gBAAgB,SAAU,GAAG;AAC7B,MAAI,EAAE,SAAS;AACf,MAAI,MAAM,QAAQ;AACd,WAAO;AAAA,EACX;AACA,MAAI,EAAE,SAAS,IAAI,GAAG;AAClB,WAAO;AAAA,EACX;AACA,MAAI,EAAE,SAAS,GAAG,GAAG;AACjB,WAAO;AAAA,EACX;AACA,MAAI,EAAE,SAAS,IAAI,GAAG;AAClB,WAAO;AAAA,EACX;AACA,MAAI,EAAE,SAAS,IAAI,GAAG;AAClB,WAAO;AAAA,EACX;AACA,MAAI,EAAE,SAAS,MAAM,GAAG;AACpB,WAAO;AAAA,EACX;AACA,MAAI,EAAE,SAAS,MAAM,GAAG;AACpB,WAAO;AAAA,EACX;AACA,SAAO,GAAG,OAAO,GAAG,IAAI;AAC5B;AACA,IAAI,eAAe,SAAU,MAAM,YAAY,YAAY,aAAa;AACpE,MAAI,QAAQ,OAAO,SAAS,UAAU;AAClC,QAAI,KAAK,SAAS,IAAI,GAAG;AACrB,aAAO,OAAO,KAAK,QAAQ,MAAM,EAAE,CAAC;AAAA,IACxC;AACA,QAAI,KAAK,SAAS,GAAG,GAAG;AACpB,UAAI,QAAQ,OAAO,KAAK,QAAQ,KAAK,EAAE,CAAC,IAAI;AAC5C,aAAO,aAAa;AAAA,IACxB;AACA,QAAI,KAAK,SAAS,IAAI,GAAG;AACrB,UAAI,QAAQ,OAAO,KAAK,QAAQ,MAAM,EAAE,CAAC,IAAI;AAC7C,aAAO,aAAa;AAAA,IACxB;AACA,QAAI,KAAK,SAAS,IAAI,GAAG;AACrB,UAAI,QAAQ,OAAO,KAAK,QAAQ,MAAM,EAAE,CAAC,IAAI;AAC7C,aAAO,cAAc;AAAA,IACzB;AAAA,EACJ;AACA,SAAO;AACX;AACA,IAAI,kBAAkB,SAAU,YAAY,YAAY,aAAa,UAAU,WAAW,UAAU,WAAW;AAC3G,aAAW,aAAa,UAAU,WAAW,OAAO,YAAY,WAAW;AAC3E,cAAY,aAAa,WAAW,WAAW,QAAQ,YAAY,WAAW;AAC9E,aAAW,aAAa,UAAU,WAAW,OAAO,YAAY,WAAW;AAC3E,cAAY,aAAa,WAAW,WAAW,QAAQ,YAAY,WAAW;AAC9E,SAAO;AAAA,IACH,UAAU,OAAO,aAAa,cAAc,SAAY,OAAO,QAAQ;AAAA,IACvE,WAAW,OAAO,cAAc,cAAc,SAAY,OAAO,SAAS;AAAA,IAC1E,UAAU,OAAO,aAAa,cAAc,SAAY,OAAO,QAAQ;AAAA,IACvE,WAAW,OAAO,cAAc,cAAc,SAAY,OAAO,SAAS;AAAA,EAC9E;AACJ;AAOA,IAAI,kBAAkB,SAAU,KAAK;AAAE,SAAQ,MAAM,QAAQ,GAAG,IAAI,MAAM,CAAC,KAAK,GAAG;AAAI;AACvF,IAAI,eAAe;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AAEA,IAAI,gBAAgB;AACpB,IAAI;AAAA;AAAA,EAA2B,SAAU,QAAQ;AAC7C,cAAUC,YAAW,MAAM;AAC3B,aAASA,WAAU,OAAO;AACtB,UAAI,IAAI,IAAI,IAAI;AAChB,UAAI,QAAQ,OAAO,KAAK,MAAM,KAAK,KAAK;AACxC,YAAM,QAAQ;AACd,YAAM,YAAY;AAElB,YAAM,aAAa;AACnB,YAAM,YAAY;AAElB,YAAM,gBAAgB;AACtB,YAAM,iBAAiB;AACvB,YAAM,eAAe;AACrB,YAAM,kBAAkB;AAExB,YAAM,aAAa;AACnB,YAAM,YAAY;AAClB,YAAM,QAAQ;AAAA,QACV,OAAO;AAAA,QACP,QAAQ;AAAA,MACZ;AACA,YAAM,aAAa,WAAY;AAC3B,YAAI,CAAC,MAAM,aAAa,CAAC,MAAM,QAAQ;AACnC,iBAAO;AAAA,QACX;AACA,YAAI,SAAS,MAAM;AACnB,YAAI,CAAC,QAAQ;AACT,iBAAO;AAAA,QACX;AACA,YAAI,UAAU,MAAM,OAAO,SAAS,cAAc,KAAK;AACvD,gBAAQ,MAAM,QAAQ;AACtB,gBAAQ,MAAM,SAAS;AACvB,gBAAQ,MAAM,WAAW;AACzB,gBAAQ,MAAM,YAAY;AAC1B,gBAAQ,MAAM,OAAO;AACrB,gBAAQ,MAAM,OAAO;AACrB,YAAI,QAAQ,WAAW;AACnB,kBAAQ,UAAU,IAAI,aAAa;AAAA,QACvC,OACK;AACD,kBAAQ,aAAa;AAAA,QACzB;AACA,eAAO,YAAY,OAAO;AAC1B,eAAO;AAAA,MACX;AACA,YAAM,aAAa,SAAU,MAAM;AAC/B,YAAI,SAAS,MAAM;AACnB,YAAI,CAAC,QAAQ;AACT;AAAA,QACJ;AACA,eAAO,YAAY,IAAI;AAAA,MAC3B;AACA,YAAM,QAAQ;AAAA,QACV,YAAY;AAAA,QACZ,QAAQ,MAAM,KAAK,MAAM,eAAe,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW,QAAQ,OAAO,SAAS,KAAK;AAAA,QACpH,SAAS,MAAM,KAAK,MAAM,eAAe,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY,QAAQ,OAAO,SAAS,KAAK;AAAA,QACtH,WAAW;AAAA,QACX,UAAU;AAAA,UACN,GAAG;AAAA,UACH,GAAG;AAAA,UACH,OAAO;AAAA,UACP,QAAQ;AAAA,QACZ;AAAA,QACA,iBAAiB;AAAA,UACb,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,iBAAiB;AAAA,UACjB,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,KAAK;AAAA,UACL,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,OAAO;AAAA,QACX;AAAA,QACA,WAAW;AAAA,MACf;AACA,YAAM,gBAAgB,MAAM,cAAc,KAAK,KAAK;AACpD,YAAM,cAAc,MAAM,YAAY,KAAK,KAAK;AAChD,YAAM,YAAY,MAAM,UAAU,KAAK,KAAK;AAC5C,aAAO;AAAA,IACX;AACA,WAAO,eAAeA,WAAU,WAAW,cAAc;AAAA,MACrD,KAAK,WAAY;AACb,YAAI,CAAC,KAAK,WAAW;AACjB,iBAAO;AAAA,QACX;AACA,eAAO,KAAK,UAAU;AAAA,MAC1B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,WAAO,eAAeA,WAAU,WAAW,UAAU;AAAA,MACjD,KAAK,WAAY;AACb,YAAI,CAAC,KAAK,WAAW;AACjB,iBAAO;AAAA,QACX;AACA,YAAI,CAAC,KAAK,UAAU,eAAe;AAC/B,iBAAO;AAAA,QACX;AACA,eAAO,KAAK,UAAU,cAAc;AAAA,MACxC;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,WAAO,eAAeA,WAAU,WAAW,aAAa;AAAA,MACpD,KAAK,WAAY;AACb,eAAO,KAAK,MAAM,QAAQ,KAAK,MAAM,eAAe;AAAA,MACxD;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,WAAO,eAAeA,WAAU,WAAW,QAAQ;AAAA,MAC/C,KAAK,WAAY;AACb,YAAI,QAAQ;AACZ,YAAI,SAAS;AACb,YAAI,KAAK,aAAa,KAAK,QAAQ;AAC/B,cAAI,WAAW,KAAK,UAAU;AAC9B,cAAI,YAAY,KAAK,UAAU;AAG/B,cAAI,cAAc,KAAK,UAAU,MAAM;AACvC,cAAI,gBAAgB,YAAY;AAC5B,iBAAK,UAAU,MAAM,WAAW;AAAA,UACpC;AAEA,kBAAQ,KAAK,UAAU,MAAM,UAAU,SAAS,KAAK,UAAU,cAAc;AAC7E,mBAAS,KAAK,UAAU,MAAM,WAAW,SAAS,KAAK,UAAU,eAAe;AAEhF,eAAK,UAAU,MAAM,WAAW;AAAA,QACpC;AACA,eAAO,EAAE,OAAc,OAAe;AAAA,MAC1C;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,WAAO,eAAeA,WAAU,WAAW,aAAa;AAAA,MACpD,KAAK,WAAY;AACb,YAAI,QAAQ;AACZ,YAAI,OAAO,KAAK,MAAM;AACtB,YAAI,UAAU,SAAU,KAAK;AACzB,cAAI;AACJ,cAAI,OAAO,MAAM,MAAM,GAAG,MAAM,eAAe,MAAM,MAAM,GAAG,MAAM,QAAQ;AACxE,mBAAO;AAAA,UACX;AACA,cAAI,MAAM,aAAa,MAAM,UAAU,GAAG,OAAO,KAAK,MAAM,UAAU,GAAG,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS,EAAE,SAAS,GAAG,IAAI;AAC3I,gBAAI,MAAM,MAAM,GAAG,EAAE,SAAS,EAAE,SAAS,GAAG,GAAG;AAC3C,qBAAO,MAAM,MAAM,GAAG,EAAE,SAAS;AAAA,YACrC;AACA,gBAAI,aAAa,MAAM,cAAc;AACrC,gBAAI,QAAQ,OAAO,MAAM,MAAM,GAAG,EAAE,SAAS,EAAE,QAAQ,MAAM,EAAE,CAAC;AAChE,gBAAI,UAAW,QAAQ,WAAW,GAAG,IAAK;AAC1C,mBAAO,GAAG,OAAO,SAAS,GAAG;AAAA,UACjC;AACA,iBAAO,cAAc,MAAM,MAAM,GAAG,CAAC;AAAA,QACzC;AACA,YAAI,QAAQ,QAAQ,OAAO,KAAK,UAAU,eAAe,CAAC,KAAK,MAAM,aAC/D,cAAc,KAAK,KAAK,IACxB,QAAQ,OAAO;AACrB,YAAI,SAAS,QAAQ,OAAO,KAAK,WAAW,eAAe,CAAC,KAAK,MAAM,aACjE,cAAc,KAAK,MAAM,IACzB,QAAQ,QAAQ;AACtB,eAAO,EAAE,OAAc,OAAe;AAAA,MAC1C;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,IAAAA,WAAU,UAAU,gBAAgB,WAAY;AAC5C,UAAI,CAAC,KAAK,YAAY;AAClB,YAAI,CAAC,KAAK,QAAQ;AACd,iBAAO,EAAE,OAAO,GAAG,QAAQ,EAAE;AAAA,QACjC;AACA,eAAO,EAAE,OAAO,KAAK,OAAO,YAAY,QAAQ,KAAK,OAAO,YAAY;AAAA,MAC5E;AACA,UAAI,OAAO,KAAK,WAAW;AAC3B,UAAI,CAAC,MAAM;AACP,eAAO,EAAE,OAAO,GAAG,QAAQ,EAAE;AAAA,MACjC;AAEA,UAAI,cAAc;AAClB,UAAI,OAAO,KAAK,WAAW,MAAM;AACjC,UAAI,SAAS,QAAQ;AACjB,sBAAc;AACd,aAAK,WAAW,MAAM,WAAW;AAAA,MAErC;AACA,WAAK,MAAM,WAAW;AACtB,WAAK,MAAM,WAAW;AACtB,WAAK,MAAM,YAAY;AACvB,UAAI,OAAO;AAAA,QACP,OAAO,KAAK;AAAA,QACZ,QAAQ,KAAK;AAAA,MACjB;AACA,UAAI,aAAa;AACb,aAAK,WAAW,MAAM,WAAW;AAAA,MACrC;AACA,WAAK,WAAW,IAAI;AACpB,aAAO;AAAA,IACX;AACA,IAAAA,WAAU,UAAU,aAAa,WAAY;AACzC,UAAI,KAAK,QAAQ;AACb,aAAK,OAAO,iBAAiB,WAAW,KAAK,SAAS;AACtD,aAAK,OAAO,iBAAiB,aAAa,KAAK,WAAW;AAC1D,aAAK,OAAO,iBAAiB,cAAc,KAAK,SAAS;AACzD,aAAK,OAAO,iBAAiB,aAAa,KAAK,aAAa;AAAA,UACxD,SAAS;AAAA,UACT,SAAS;AAAA,QACb,CAAC;AACD,aAAK,OAAO,iBAAiB,YAAY,KAAK,SAAS;AAAA,MAC3D;AAAA,IACJ;AACA,IAAAA,WAAU,UAAU,eAAe,WAAY;AAC3C,UAAI,KAAK,QAAQ;AACb,aAAK,OAAO,oBAAoB,WAAW,KAAK,SAAS;AACzD,aAAK,OAAO,oBAAoB,aAAa,KAAK,WAAW;AAC7D,aAAK,OAAO,oBAAoB,cAAc,KAAK,SAAS;AAC5D,aAAK,OAAO,oBAAoB,aAAa,KAAK,aAAa,IAAI;AACnE,aAAK,OAAO,oBAAoB,YAAY,KAAK,SAAS;AAAA,MAC9D;AAAA,IACJ;AACA,IAAAA,WAAU,UAAU,oBAAoB,WAAY;AAChD,UAAI,CAAC,KAAK,aAAa,CAAC,KAAK,QAAQ;AACjC;AAAA,MACJ;AACA,UAAI,gBAAgB,KAAK,OAAO,iBAAiB,KAAK,SAAS;AAC/D,WAAK,SAAS;AAAA,QACV,OAAO,KAAK,MAAM,SAAS,KAAK,KAAK;AAAA,QACrC,QAAQ,KAAK,MAAM,UAAU,KAAK,KAAK;AAAA,QACvC,WAAW,cAAc,cAAc,SAAS,cAAc,YAAY;AAAA,MAC9E,CAAC;AAAA,IACL;AACA,IAAAA,WAAU,UAAU,uBAAuB,WAAY;AACnD,UAAI,KAAK,QAAQ;AACb,aAAK,aAAa;AAAA,MACtB;AAAA,IACJ;AACA,IAAAA,WAAU,UAAU,2BAA2B,SAAU,SAAS,MAAM;AACpE,UAAI,YAAY,KAAK,aAAa,KAAK,UAAU,IAAI;AACrD,aAAO,KAAK,MAAM,IAAI,MAAM,UACxB,KAAK,MAAM,SAAS,IAAI,MAAM,YAC7B,OAAO,cAAc,eAAe,cAAc,UACjD,SACA;AAAA,IACV;AACA,IAAAA,WAAU,UAAU,8BAA8B,SAAU,UAAU,WAAW;AAC7E,UAAI,oBAAoB,KAAK,MAAM;AACnC,UAAI,YAAY,KAAK,MAAM;AAC3B,UAAI,mBAAmB,qBAAqB,aAAa,QAAQ,SAAS;AAC1E,UAAI,oBAAoB,qBAAqB,aAAa,OAAO,SAAS;AAC1E,UAAI;AACJ,UAAI;AACJ,UAAI,KAAK,MAAM,WAAW,UAAU;AAChC,YAAI,WAAW,KAAK;AACpB,YAAI,UAAU;AACV,uBAAa,mBACP,KAAK,iBAAiB,KAAK,aAC3B,SAAS,eAAe,KAAK,aAAa,KAAK;AACrD,wBAAc,oBACR,KAAK,kBAAkB,KAAK,YAC5B,SAAS,gBAAgB,KAAK,YAAY,KAAK;AAAA,QACzD;AAAA,MACJ,WACS,KAAK,MAAM,WAAW,UAAU;AACrC,YAAI,KAAK,QAAQ;AACb,uBAAa,mBAAmB,KAAK,iBAAiB,KAAK,OAAO,aAAa,KAAK;AACpF,wBAAc,oBAAoB,KAAK,kBAAkB,KAAK,OAAO,cAAc,KAAK;AAAA,QAC5F;AAAA,MACJ,WACS,KAAK,MAAM,QAAQ;AACxB,qBAAa,mBACP,KAAK,iBAAiB,KAAK,aAC3B,KAAK,MAAM,OAAO,eAAe,KAAK,aAAa,KAAK;AAC9D,sBAAc,oBACR,KAAK,kBAAkB,KAAK,YAC5B,KAAK,MAAM,OAAO,gBAAgB,KAAK,YAAY,KAAK;AAAA,MAClE;AACA,UAAI,cAAc,OAAO,SAAS,UAAU,GAAG;AAC3C,mBAAW,YAAY,WAAW,aAAa,WAAW;AAAA,MAC9D;AACA,UAAI,eAAe,OAAO,SAAS,WAAW,GAAG;AAC7C,oBAAY,aAAa,YAAY,cAAc,YAAY;AAAA,MACnE;AACA,aAAO,EAAE,UAAoB,UAAqB;AAAA,IACtD;AACA,IAAAA,WAAU,UAAU,gCAAgC,SAAU,SAAS,SAAS;AAC5E,UAAI,QAAQ,KAAK,MAAM,SAAS;AAChC,UAAI,KAAK,gBAAgB,KAAK,MAAM,eAAe,CAAC,GAAG,eAAe,GAAG,CAAC,GAAG,eAAe,GAAG,CAAC;AAChG,UAAI,KAAK,KAAK,OAAO,YAAY,GAAG,WAAW,WAAW,GAAG;AAC7D,UAAI,KAAK,KAAK,OAAO,kBAAkB,GAAG,iBAAiB,6BAA6B,GAAG,4BAA4B,4BAA4B,GAAG;AACtJ,UAAI,WAAW,SAAS;AACxB,UAAI,YAAY,SAAS;AACzB,UAAI,cAAc,8BAA8B;AAChD,UAAI,aAAa,6BAA6B;AAC9C,UAAI,aAAa,SAAS,SAAS,GAAG;AAClC,mBAAW,SAAS,SAAU,UAAU,SAAS,KAAK,eAAgB;AACtE,YAAI,iBAAiB;AACjB,uBAAa,WAAW,cAAc,KAAK,QAAQ;AAAA,QACvD;AAAA,MACJ;AACA,UAAI,aAAa,QAAQ,SAAS,GAAG;AACjC,mBAAW,SAAS,SAAU,UAAU,SAAS,KAAK,eAAgB;AACtE,YAAI,iBAAiB;AACjB,uBAAa,WAAW,cAAc,KAAK,QAAQ;AAAA,QACvD;AAAA,MACJ;AACA,UAAI,aAAa,UAAU,SAAS,GAAG;AACnC,oBAAY,SAAS,UAAW,UAAU,SAAS,KAAK,eAAgB;AACxE,YAAI,iBAAiB;AACjB,sBAAY,YAAY,eAAe,KAAK,QAAQ;AAAA,QACxD;AAAA,MACJ;AACA,UAAI,aAAa,OAAO,SAAS,GAAG;AAChC,oBAAY,SAAS,UAAW,UAAU,SAAS,KAAK,eAAgB;AACxE,YAAI,iBAAiB;AACjB,sBAAY,YAAY,eAAe,KAAK,QAAQ;AAAA,QACxD;AAAA,MACJ;AACA,aAAO,EAAE,UAAoB,UAAqB;AAAA,IACtD;AACA,IAAAA,WAAU,UAAU,kCAAkC,SAAU,UAAU,WAAW,KAAK,KAAK;AAC3F,UAAI,KAAK,KAAK,OAAO,kBAAkB,GAAG,iBAAiB,6BAA6B,GAAG,4BAA4B,4BAA4B,GAAG;AACtJ,UAAI,mBAAmB,OAAO,IAAI,UAAU,cAAc,KAAK,IAAI;AACnE,UAAI,mBAAmB,OAAO,IAAI,UAAU,eAAe,IAAI,QAAQ,IAAI,WAAW,IAAI;AAC1F,UAAI,oBAAoB,OAAO,IAAI,WAAW,cAAc,KAAK,IAAI;AACrE,UAAI,oBAAoB,OAAO,IAAI,WAAW,eAAe,IAAI,SAAS,IAAI,YAAY,IAAI;AAC9F,UAAI,cAAc,8BAA8B;AAChD,UAAI,aAAa,6BAA6B;AAC9C,UAAI,iBAAiB;AACjB,YAAI,iBAAiB,oBAAoB,eAAe,KAAK,QAAQ;AACrE,YAAI,iBAAiB,oBAAoB,eAAe,KAAK,QAAQ;AACrE,YAAI,kBAAkB,mBAAmB,cAAc,KAAK,QAAQ;AACpE,YAAI,kBAAkB,mBAAmB,cAAc,KAAK,QAAQ;AACpE,YAAI,iBAAiB,KAAK,IAAI,kBAAkB,aAAa;AAC7D,YAAI,iBAAiB,KAAK,IAAI,kBAAkB,aAAa;AAC7D,YAAI,kBAAkB,KAAK,IAAI,mBAAmB,cAAc;AAChE,YAAI,kBAAkB,KAAK,IAAI,mBAAmB,cAAc;AAChE,mBAAW,MAAM,UAAU,gBAAgB,cAAc;AACzD,oBAAY,MAAM,WAAW,iBAAiB,eAAe;AAAA,MACjE,OACK;AACD,mBAAW,MAAM,UAAU,kBAAkB,gBAAgB;AAC7D,oBAAY,MAAM,WAAW,mBAAmB,iBAAiB;AAAA,MACrE;AACA,aAAO,EAAE,UAAoB,UAAqB;AAAA,IACtD;AACA,IAAAA,WAAU,UAAU,wBAAwB,WAAY;AACpD,UAAI,gBAAgB,KAAK,KAAK,MAAM,SAAS;AAE7C,UAAI,KAAK,MAAM,WAAW,UAAU;AAChC,YAAI,WAAW,KAAK;AACpB,YAAI,UAAU;AACV,cAAI,aAAa,SAAS,sBAAsB;AAChD,eAAK,aAAa,WAAW,OAAO;AACpC,eAAK,YAAY,WAAW,MAAM;AAAA,QACtC;AAAA,MACJ;AAEA,UAAI,KAAK,MAAM,UAAU,OAAO,KAAK,MAAM,WAAW,UAAU;AAC5D,YAAI,aAAa,KAAK,MAAM,OAAO,sBAAsB;AACzD,aAAK,aAAa,WAAW,OAAO;AACpC,aAAK,YAAY,WAAW,MAAM;AAAA,MACtC;AAEA,UAAI,KAAK,WAAW;AAChB,YAAI,KAAK,KAAK,UAAU,sBAAsB,GAAG,OAAO,GAAG,MAAM,QAAQ,GAAG,KAAK,QAAQ,GAAG,OAAO,SAAS,GAAG;AAC/G,aAAK,gBAAgB,OAAO;AAC5B,aAAK,iBAAiB,QAAQ;AAC9B,aAAK,eAAe,QAAQ;AAC5B,aAAK,kBAAkB,SAAS;AAAA,MACpC;AAAA,IACJ;AACA,IAAAA,WAAU,UAAU,gBAAgB,SAAU,OAAO,WAAW;AAC5D,UAAI,CAAC,KAAK,aAAa,CAAC,KAAK,QAAQ;AACjC;AAAA,MACJ;AACA,UAAI,UAAU;AACd,UAAI,UAAU;AACd,UAAI,MAAM,eAAe,aAAa,MAAM,WAAW,GAAG;AACtD,kBAAU,MAAM,YAAY;AAC5B,kBAAU,MAAM,YAAY;AAAA,MAChC,WACS,MAAM,eAAe,aAAa,MAAM,WAAW,GAAG;AAC3D,kBAAU,MAAM,YAAY,QAAQ,CAAC,EAAE;AACvC,kBAAU,MAAM,YAAY,QAAQ,CAAC,EAAE;AAAA,MAC3C;AACA,UAAI,KAAK,MAAM,eAAe;AAC1B,YAAI,KAAK,WAAW;AAChB,cAAI,cAAc,KAAK,MAAM,cAAc,OAAO,WAAW,KAAK,SAAS;AAC3E,cAAI,gBAAgB,OAAO;AACvB;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAEA,UAAI,KAAK,MAAM,MAAM;AACjB,YAAI,OAAO,KAAK,MAAM,KAAK,WAAW,eAAe,KAAK,MAAM,KAAK,WAAW,KAAK,MAAM,QAAQ;AAC/F,eAAK,SAAS,EAAE,QAAQ,KAAK,MAAM,KAAK,OAAO,CAAC;AAAA,QACpD;AACA,YAAI,OAAO,KAAK,MAAM,KAAK,UAAU,eAAe,KAAK,MAAM,KAAK,UAAU,KAAK,MAAM,OAAO;AAC5F,eAAK,SAAS,EAAE,OAAO,KAAK,MAAM,KAAK,MAAM,CAAC;AAAA,QAClD;AAAA,MACJ;AAEA,WAAK,QACD,OAAO,KAAK,MAAM,oBAAoB,WAAW,KAAK,MAAM,kBAAkB,KAAK,KAAK,QAAQ,KAAK,KAAK;AAC9G,UAAI;AACJ,UAAI,gBAAgB,KAAK,OAAO,iBAAiB,KAAK,SAAS;AAC/D,UAAI,cAAc,cAAc,QAAQ;AACpC,YAAI,WAAW,KAAK;AACpB,YAAI,UAAU;AACV,cAAI,MAAM,KAAK,OAAO,iBAAiB,QAAQ,EAAE;AACjD,eAAK,UAAU,IAAI,WAAW,KAAK,IAAI,QAAQ;AAC/C,sBAAY,cAAc;AAAA,QAC9B;AAAA,MACJ;AAEA,WAAK,sBAAsB;AAC3B,WAAK,WAAW;AAChB,UAAI,QAAQ;AAAA,QACR,UAAU;AAAA,UACN,GAAG;AAAA,UACH,GAAG;AAAA,UACH,OAAO,KAAK,KAAK;AAAA,UACjB,QAAQ,KAAK,KAAK;AAAA,QACtB;AAAA,QACA,YAAY;AAAA,QACZ,iBAAiBD,UAASA,UAAS,CAAC,GAAG,KAAK,MAAM,eAAe,GAAG,EAAE,QAAQ,KAAK,OAAO,iBAAiB,MAAM,MAAM,EAAE,UAAU,OAAO,CAAC;AAAA,QAC3I;AAAA,QACA;AAAA,MACJ;AACA,WAAK,SAAS,KAAK;AAAA,IACvB;AACA,IAAAC,WAAU,UAAU,cAAc,SAAU,OAAO;AAC/C,UAAI,QAAQ;AACZ,UAAI,CAAC,KAAK,MAAM,cAAc,CAAC,KAAK,aAAa,CAAC,KAAK,QAAQ;AAC3D;AAAA,MACJ;AACA,UAAI,KAAK,OAAO,cAAc,aAAa,KAAK,GAAG;AAC/C,YAAI;AACA,gBAAM,eAAe;AACrB,gBAAM,gBAAgB;AAAA,QAC1B,SACO,GAAG;AAAA,QAEV;AAAA,MACJ;AACA,UAAI,KAAK,KAAK,OAAO,WAAW,GAAG,UAAU,YAAY,GAAG,WAAW,WAAW,GAAG,UAAU,YAAY,GAAG;AAC9G,UAAI,UAAU,aAAa,KAAK,IAAI,MAAM,QAAQ,CAAC,EAAE,UAAU,MAAM;AACrE,UAAI,UAAU,aAAa,KAAK,IAAI,MAAM,QAAQ,CAAC,EAAE,UAAU,MAAM;AACrE,UAAI,KAAK,KAAK,OAAO,YAAY,GAAG,WAAW,WAAW,GAAG,UAAU,QAAQ,GAAG,OAAO,SAAS,GAAG;AACrG,UAAI,aAAa,KAAK,cAAc;AACpC,UAAI,MAAM,gBAAgB,YAAY,KAAK,OAAO,YAAY,KAAK,OAAO,aAAa,UAAU,WAAW,UAAU,SAAS;AAC/H,iBAAW,IAAI;AACf,kBAAY,IAAI;AAChB,iBAAW,IAAI;AACf,kBAAY,IAAI;AAEhB,UAAI,KAAK,KAAK,8BAA8B,SAAS,OAAO,GAAG,YAAY,GAAG,WAAW,WAAW,GAAG;AAEvG,UAAI,cAAc,KAAK,4BAA4B,UAAU,SAAS;AACtE,UAAI,KAAK,MAAM,QAAQ,KAAK,MAAM,KAAK,GAAG;AACtC,mBAAW,gBAAgB,UAAU,KAAK,MAAM,KAAK,GAAG,KAAK,MAAM,OAAO;AAAA,MAC9E;AACA,UAAI,KAAK,MAAM,QAAQ,KAAK,MAAM,KAAK,GAAG;AACtC,oBAAY,gBAAgB,WAAW,KAAK,MAAM,KAAK,GAAG,KAAK,MAAM,OAAO;AAAA,MAChF;AAEA,UAAI,UAAU,KAAK,gCAAgC,UAAU,WAAW,EAAE,OAAO,YAAY,UAAU,QAAQ,YAAY,UAAU,GAAG,EAAE,OAAO,UAAU,QAAQ,UAAU,CAAC;AAC9K,iBAAW,QAAQ;AACnB,kBAAY,QAAQ;AACpB,UAAI,KAAK,MAAM,MAAM;AACjB,YAAI,eAAe,KAAK,UAAU,KAAK,MAAM,KAAK,CAAC,GAAG,KAAK,MAAM,UAAU,KAAK,MAAM,QAAQ,CAAC,IAAI,CAAC;AACpG,YAAI,gBAAgB,KAAK,WAAW,KAAK,MAAM,KAAK,CAAC,GAAG,KAAK,MAAM,UAAU,KAAK,MAAM,QAAQ,CAAC,IAAI,CAAC;AACtG,YAAI,MAAM,KAAK,MAAM,WAAW;AAChC,YAAI,IAAI,QAAQ,KAAK,KAAK,IAAI,eAAe,QAAQ,KAAK,MAAM,eAAe;AAC/E,YAAI,IAAI,QAAQ,KAAK,KAAK,IAAI,gBAAgB,SAAS,KAAK,MAAM,gBAAgB;AAClF,mBAAW;AACX,oBAAY;AAAA,MAChB;AACA,UAAI,QAAQ;AAAA,QACR,OAAO,WAAW,SAAS;AAAA,QAC3B,QAAQ,YAAY,SAAS;AAAA,MACjC;AACA,WAAK,QAAQ;AACb,UAAI,SAAS,OAAO,UAAU,UAAU;AACpC,YAAI,MAAM,SAAS,GAAG,GAAG;AACrB,cAAI,UAAW,WAAW,WAAW,QAAS;AAC9C,qBAAW,GAAG,OAAO,SAAS,GAAG;AAAA,QACrC,WACS,MAAM,SAAS,IAAI,GAAG;AAC3B,cAAI,KAAM,WAAW,KAAK,OAAO,aAAc;AAC/C,qBAAW,GAAG,OAAO,IAAI,IAAI;AAAA,QACjC,WACS,MAAM,SAAS,IAAI,GAAG;AAC3B,cAAI,KAAM,WAAW,KAAK,OAAO,cAAe;AAChD,qBAAW,GAAG,OAAO,IAAI,IAAI;AAAA,QACjC;AAAA,MACJ;AACA,UAAI,UAAU,OAAO,WAAW,UAAU;AACtC,YAAI,OAAO,SAAS,GAAG,GAAG;AACtB,cAAI,UAAW,YAAY,WAAW,SAAU;AAChD,sBAAY,GAAG,OAAO,SAAS,GAAG;AAAA,QACtC,WACS,OAAO,SAAS,IAAI,GAAG;AAC5B,cAAI,KAAM,YAAY,KAAK,OAAO,aAAc;AAChD,sBAAY,GAAG,OAAO,IAAI,IAAI;AAAA,QAClC,WACS,OAAO,SAAS,IAAI,GAAG;AAC5B,cAAI,KAAM,YAAY,KAAK,OAAO,cAAe;AACjD,sBAAY,GAAG,OAAO,IAAI,IAAI;AAAA,QAClC;AAAA,MACJ;AACA,UAAI,WAAW;AAAA,QACX,OAAO,KAAK,yBAAyB,UAAU,OAAO;AAAA,QACtD,QAAQ,KAAK,yBAAyB,WAAW,QAAQ;AAAA,MAC7D;AACA,UAAI,KAAK,YAAY,OAAO;AACxB,iBAAS,YAAY,SAAS;AAAA,MAClC,WACS,KAAK,YAAY,UAAU;AAChC,iBAAS,YAAY,SAAS;AAAA,MAClC;AACA,UAAI,eAAe,KAAK,MAAM,UAAU,SAAS;AACjD,UAAI,gBAAgB,KAAK,MAAM,WAAW,SAAS;AACnD,UAAI,kBAAkB,KAAK,MAAM,cAAc,SAAS;AACxD,UAAI,UAAU,gBAAgB,iBAAiB;AAC/C,UAAI,SAAS;AAET,wCAAU,WAAY;AAClB,gBAAM,SAAS,QAAQ;AAAA,QAC3B,CAAC;AAAA,MACL;AACA,UAAI,KAAK,MAAM,UAAU;AACrB,YAAI,SAAS;AACT,eAAK,MAAM,SAAS,OAAO,WAAW,KAAK,WAAW,KAAK;AAAA,QAC/D;AAAA,MACJ;AAAA,IACJ;AACA,IAAAA,WAAU,UAAU,YAAY,SAAU,OAAO;AAC7C,UAAI,IAAI;AACR,UAAI,KAAK,KAAK,OAAO,aAAa,GAAG,YAAY,YAAY,GAAG,WAAW,WAAW,GAAG;AACzF,UAAI,CAAC,cAAc,CAAC,KAAK,WAAW;AAChC;AAAA,MACJ;AACA,UAAI,KAAK,MAAM,cAAc;AACzB,aAAK,MAAM,aAAa,OAAO,WAAW,KAAK,WAAW,KAAK,KAAK;AAAA,MACxE;AACA,UAAI,KAAK,MAAM,MAAM;AACjB,aAAK,SAAS,EAAE,QAAQ,KAAK,KAAK,MAAM,KAAK,WAAW,QAAQ,OAAO,SAAS,KAAK,QAAQ,SAAS,KAAK,KAAK,MAAM,KAAK,YAAY,QAAQ,OAAO,SAAS,KAAK,OAAO,CAAC;AAAA,MAChL;AACA,WAAK,aAAa;AAClB,WAAK,SAAS;AAAA,QACV,YAAY;AAAA,QACZ,iBAAiBD,UAASA,UAAS,CAAC,GAAG,KAAK,MAAM,eAAe,GAAG,EAAE,QAAQ,OAAO,CAAC;AAAA,MAC1F,CAAC;AAAA,IACL;AACA,IAAAC,WAAU,UAAU,aAAa,SAAU,MAAM;AAC7C,UAAI,IAAI;AACR,WAAK,SAAS,EAAE,QAAQ,KAAK,KAAK,WAAW,QAAQ,OAAO,SAAS,KAAK,QAAQ,SAAS,KAAK,KAAK,YAAY,QAAQ,OAAO,SAAS,KAAK,OAAO,CAAC;AAAA,IAC1J;AACA,IAAAA,WAAU,UAAU,gBAAgB,WAAY;AAC5C,UAAI,QAAQ;AACZ,UAAI,KAAK,KAAK,OAAO,SAAS,GAAG,QAAQ,eAAe,GAAG,cAAc,gBAAgB,GAAG,eAAe,qBAAqB,GAAG,oBAAoB,qBAAqB,GAAG,oBAAoB,kBAAkB,GAAG;AACxN,UAAI,CAAC,QAAQ;AACT,eAAO;AAAA,MACX;AACA,UAAI,WAAW,OAAO,KAAK,MAAM,EAAE,IAAI,SAAU,KAAK;AAClD,YAAI,OAAO,GAAG,MAAM,OAAO;AACvB,qBAAQ,oBAAAC,KAAK,SAAS,EAAE,WAAW,KAAK,eAAe,MAAM,eAAe,eAAe,gBAAgB,aAAa,GAAG,GAAG,WAAW,iBAAiB,cAAc,GAAG,GAAG,UAAU,mBAAmB,gBAAgB,GAAG,IAAI,gBAAgB,GAAG,IAAI,KAAK,GAAG,GAAG;AAAA,QACxQ;AACA,eAAO;AAAA,MACX,CAAC;AAED,iBAAQ,oBAAAA,KAAK,OAAO,EAAE,WAAW,oBAAoB,OAAO,oBAAoB,UAAU,SAAS,CAAC;AAAA,IACxG;AACA,IAAAD,WAAU,UAAU,SAAS,WAAY;AACrC,UAAI,QAAQ;AACZ,UAAI,eAAe,OAAO,KAAK,KAAK,KAAK,EAAE,OAAO,SAAU,KAAK,KAAK;AAClE,YAAI,aAAa,QAAQ,GAAG,MAAM,IAAI;AAClC,iBAAO;AAAA,QACX;AACA,YAAI,GAAG,IAAI,MAAM,MAAM,GAAG;AAC1B,eAAO;AAAA,MACX,GAAG,CAAC,CAAC;AACL,UAAI,QAAQD,UAASA,UAASA,UAAS,EAAE,UAAU,YAAY,YAAY,KAAK,MAAM,aAAa,SAAS,OAAO,GAAG,KAAK,MAAM,KAAK,GAAG,KAAK,SAAS,GAAG,EAAE,UAAU,KAAK,MAAM,UAAU,WAAW,KAAK,MAAM,WAAW,UAAU,KAAK,MAAM,UAAU,WAAW,KAAK,MAAM,WAAW,WAAW,cAAc,YAAY,EAAE,CAAC;AACpU,UAAI,KAAK,MAAM,WAAW;AACtB,cAAM,YAAY,KAAK,MAAM;AAAA,MACjC;AACA,UAAI,UAAU,KAAK,MAAM,MAAM;AAC/B,iBAAQ,oBAAAG,MAAM,SAASH,UAAS,EAAE,OAAc,WAAW,KAAK,MAAM,UAAU,GAAG,cAAc;AAAA;AAAA;AAAA,QAG7F,KAAK,SAAU,GAAG;AACd,cAAI,GAAG;AACH,kBAAM,YAAY;AAAA,UACtB;AAAA,QACJ;AAAA,QAAG,UAAU,CAAC,KAAK,MAAM,kBAAc,oBAAAE,KAAK,OAAO,EAAE,OAAO,KAAK,MAAM,gBAAgB,CAAC,GAAG,KAAK,MAAM,UAAU,KAAK,cAAc,CAAC;AAAA,MAAE,CAAC,CAAC;AAAA,IAChJ;AACA,IAAAD,WAAU,eAAe;AAAA,MACrB,IAAI;AAAA,MACJ,eAAe,WAAY;AAAA,MAAE;AAAA,MAC7B,UAAU,WAAY;AAAA,MAAE;AAAA,MACxB,cAAc,WAAY;AAAA,MAAE;AAAA,MAC5B,QAAQ;AAAA,QACJ,KAAK;AAAA,QACL,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,UAAU;AAAA,QACV,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,SAAS;AAAA,MACb;AAAA,MACA,OAAO,CAAC;AAAA,MACR,MAAM,CAAC,GAAG,CAAC;AAAA,MACX,SAAS,CAAC,GAAG,CAAC;AAAA,MACd,iBAAiB;AAAA,MACjB,2BAA2B;AAAA,MAC3B,4BAA4B;AAAA,MAC5B,OAAO;AAAA,MACP,aAAa;AAAA,MACb,SAAS;AAAA,IACb;AACA,WAAOA;AAAA,EACX,EAAE,2BAAa;AAAA;", "names": ["import_jsx_runtime", "import_react", "_jsx", "d", "b", "__assign", "Resizable", "_jsx", "_jsxs"]}