{"name": "codemirror", "version": "5.65.20", "main": "lib/codemirror.js", "style": "lib/codemirror.css", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://marijnhaverbeke.nl"}, "description": "Full-featured in-browser code editor", "license": "MIT", "directories": {"lib": "./lib"}, "scripts": {"build": "rollup -c", "watch": "rollup -w -c", "prepare": "npm run-script build", "test": "node ./test/run.js", "lint": "bin/lint"}, "devDependencies": {"@rollup/plugin-buble": "^0.21.3", "blint": "^1.1.2", "cm5-vim": "^0.0.5", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3", "rollup-plugin-copy": "^3.4.0"}, "bugs": "http://github.com/codemirror/CodeMirror/issues", "keywords": ["JavaScript", "CodeMirror", "Editor"], "homepage": "https://codemirror.net/5/", "repository": {"type": "git", "url": "https://github.com/codemirror/CodeMirror.git"}, "jspm": {"directories": {}, "dependencies": {}, "devDependencies": {}}, "dependencies": {}, "publishConfig": {"tag": "version5"}}