{"name": "nexus", "version": "2.2.0", "description": "AI-friendly note-taking and productivity app for macOS", "main": "dist/main/main/main.js", "scripts": {"postinstall": "npx electron-rebuild", "predev": "rm -rf dist && npx electron-rebuild", "dev": "concurrently \"npm run dev:vite\" \"npm run dev:main\" \"npm run dev:electron\"", "dev:vite": "vite", "dev:main": "tsc -p tsconfig.main.json --watch", "dev:electron": "wait-on http://localhost:5173 && wait-on dist/main/main/main.js && mkdir -p dist/main/database && mkdir -p dist/main/assets && cp src/database/schema.sql dist/main/database/ && cp assets/tray-iconTemplate*.png assets/menubar-icon.png dist/main/assets/ 2>/dev/null || true && cp -r assets/app-icon.iconset dist/main/assets/ && node scripts/validate-dev-env.js && NODE_ENV=development electron .", "build": "npm run build:main && npm run build:renderer && npm run copy:assets", "build:main": "tsc -p tsconfig.main.json", "build:renderer": "vite build", "copy:assets": "cp src/database/schema.sql dist/main/database/ && mkdir -p dist/main/assets && cp assets/tray-iconTemplate*.png assets/menubar-icon.png dist/main/assets/ 2>/dev/null || true && cp -r assets/app-icon.iconset dist/main/assets/", "build:electron": "electron-builder", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx", "test:app": "./scripts/test-app.sh", "validate:dev": "node scripts/validate-dev-env.js", "clean": "rm -rf dist && pkill -f 'Electron.*nexus' || true"}, "keywords": ["electron", "react", "typescript", "notes", "productivity"], "author": "Nexus Team", "license": "MIT", "dependencies": {"@types/codemirror": "^5.60.16", "@types/markdown-it": "^14.1.2", "better-sqlite3": "^9.6.0", "codemirror": "^5.65.15", "markdown-it": "^14.1.0", "mathjs": "^12.4.3", "re-resizable": "^6.11.2", "react": "^18.2.0", "react-dom": "^18.2.0", "uuid": "^9.0.0", "zustand": "^4.4.0"}, "devDependencies": {"@types/better-sqlite3": "^7.6.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/uuid": "^9.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.0", "autoprefixer": "^10.4.0", "concurrently": "^8.0.0", "electron": "^27.0.0", "electron-builder": "^24.0.0", "electron-rebuild": "^3.2.9", "eslint": "^8.0.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "postcss": "^8.4.0", "sharp": "^0.34.3", "tailwindcss": "^3.3.0", "typescript": "^5.0.0", "vite": "^5.0.0", "vite-plugin-electron": "^0.15.0", "wait-on": "^7.0.0"}, "build": {"appId": "com.nexus.app", "productName": "Nexus", "directories": {"output": "dist-electron"}, "files": ["dist/**/*", "node_modules/**/*"], "mac": {"category": "public.app-category.productivity", "icon": "assets/app-icon.icns", "target": [{"target": "dmg", "arch": ["arm64", "x64"]}]}}}