"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.initializeShortcuts = initializeShortcuts;
exports.registerShortcuts = registerShortcuts;
exports.unregisterAllShortcuts = unregisterAllShortcuts;
exports.updateShortcuts = updateShortcuts;
exports.getShortcutSettings = getShortcutSettings;
const electron_1 = require("electron");
const shortcutSettings_1 = require("./shortcutSettings");
const logger_1 = require("../utils/logger");
let shortcutSettings;
let registeredShortcuts = [];
function initializeShortcuts() {
    shortcutSettings = new shortcutSettings_1.ShortcutSettingsManager();
}
function registerShortcuts(window) {
    if (!shortcutSettings) {
        initializeShortcuts();
    }
    // Unregister existing shortcuts first
    unregisterAllShortcuts();
    const settings = shortcutSettings.getSettings();
    // Define which shortcuts should be global vs local
    const globalShortcuts = ['toggle-window']; // Only window toggle should be global
    const localShortcutHandlers = {
        'create-new-note': () => showWindowAndCreateNote(window),
        'create-note': () => showWindowAndCreateNote(window) // Legacy compatibility
    };
    const globalShortcutHandlers = {
        'toggle-window': () => toggleWindow(window),
        'open-calculator': () => showWindowAndOpenCalculator(window),
        'open-clipboard': () => showWindowAndOpenClipboard(window),
        'focus-search': () => showWindowAndFocusSearch(window),
        'open-trash': () => showWindowAndOpenTrash(window),
        'toggle-sidebar': () => sendToRenderer(window, 'toggle-sidebar'),
        'show-shortcuts': () => sendToRenderer(window, 'show-shortcut-settings')
    };
    // Register global shortcuts
    for (const [action, shortcut] of Object.entries(settings.shortcuts)) {
        if (globalShortcuts.includes(action)) {
            const handler = globalShortcutHandlers[action];
            if (handler && shortcut && shortcut.trim() !== '') {
                const success = electron_1.globalShortcut.register(shortcut, handler);
                if (success) {
                    registeredShortcuts.push(shortcut);
                    logger_1.SafeLogger.info(`Registered global shortcut: ${shortcut} for ${action}`);
                }
                else {
                    logger_1.SafeLogger.error(`Failed to register global shortcut: ${shortcut} for ${action}`);
                }
            }
        }
    }
    // Register local shortcuts (handled in renderer process)
    registerLocalShortcuts(window, settings, localShortcutHandlers);
    // Cleanup shortcuts when app is quitting
    electron_1.app.on('will-quit', () => {
        unregisterAllShortcuts();
    });
}
function registerLocalShortcuts(window, settings, handlers) {
    // Send local shortcut configuration to renderer
    const localShortcuts = {};
    for (const [action, shortcut] of Object.entries(settings.shortcuts)) {
        if (handlers[action] && shortcut && typeof shortcut === 'string' && shortcut.trim() !== '') {
            localShortcuts[action] = shortcut;
            logger_1.SafeLogger.info(`Preparing local shortcut: ${action} -> ${shortcut}`);
        }
    }
    logger_1.SafeLogger.info('Local shortcuts to register:', localShortcuts);
    // Wait for renderer to be ready before sending shortcuts
    if (window.webContents.isLoading()) {
        window.webContents.once('did-finish-load', () => {
            logger_1.SafeLogger.info('Renderer ready, sending local shortcuts');
            window.webContents.send('shortcuts:register-local', localShortcuts);
        });
    }
    else {
        logger_1.SafeLogger.info('Renderer already ready, sending local shortcuts immediately');
        window.webContents.send('shortcuts:register-local', localShortcuts);
    }
    // Listen for local shortcut events from renderer via IPC
    // This will be handled by the existing create-new-note IPC message
}
function unregisterAllShortcuts() {
    electron_1.globalShortcut.unregisterAll();
    registeredShortcuts = [];
}
function updateShortcuts(window) {
    logger_1.SafeLogger.info('Updating shortcuts with new configuration');
    registerShortcuts(window);
}
function getShortcutSettings() {
    if (!shortcutSettings) {
        initializeShortcuts();
    }
    return shortcutSettings;
}
function sendToRenderer(window, channel, ...args) {
    window.webContents.send(channel, ...args);
}
function showWindowAndOpenTrash(window) {
    showWindow(window);
    sendToRenderer(window, 'switch-to-trash');
}
function toggleWindow(window) {
    console.log(`Toggle window - visible: ${window.isVisible()}, focused: ${window.isFocused()}, loading: ${window.webContents.isLoading()}`);
    if (window.isVisible() && window.isFocused()) {
        console.log('Hiding window');
        window.hide();
        // Hide from dock on macOS
        if (process.platform === 'darwin') {
            electron_1.app.dock.hide();
        }
    }
    else {
        console.log('Showing window');
        showWindow(window);
    }
}
function showWindow(window) {
    // Ensure window is ready before showing
    if (window.webContents.isLoading()) {
        console.log('Window content is loading, waiting for completion...');
        window.webContents.once('did-finish-load', () => {
            console.log('Content loaded, showing window');
            showWindowImmediate(window);
        });
    }
    else {
        showWindowImmediate(window);
    }
}
function showWindowImmediate(window) {
    console.log('showWindowImmediate called');
    // Restore window if minimized
    if (window.isMinimized()) {
        console.log('Window was minimized, restoring');
        window.restore();
    }
    console.log('Showing and focusing window');
    window.show();
    window.focus();
    // Bring to front on macOS and ensure it's visible
    if (process.platform === 'darwin') {
        electron_1.app.dock.show();
        // Force the window to the foreground
        electron_1.app.focus({ steal: true });
    }
    // Wait a bit and check if window is actually visible
    setTimeout(() => {
        console.log(`Window state after show: visible=${window.isVisible()}, focused=${window.isFocused()}`);
    }, 100);
    console.log('Window shown and focused');
}
function showWindowAndCreateNote(window) {
    showWindow(window);
    // Send event to renderer to create a new note
    window.webContents.send('create-new-note');
}
function showWindowAndOpenCalculator(window) {
    showWindow(window);
    // Send event to renderer to switch to calculator
    window.webContents.send('switch-to-calculator');
}
function showWindowAndOpenClipboard(window) {
    showWindow(window);
    // Send event to renderer to switch to clipboard
    window.webContents.send('switch-to-clipboard');
}
function showWindowAndFocusSearch(window) {
    showWindow(window);
    // Send event to renderer to focus search input
    window.webContents.send('focus-search');
}
