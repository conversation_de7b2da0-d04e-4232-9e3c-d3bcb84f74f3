"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SimpleTray = void 0;
const electron_1 = require("electron");
const path_1 = require("path");
class SimpleTray {
    constructor(window) {
        this.window = window;
        this.createTray();
    }
    createTray() {
        // Create a simple tray icon (we'll use a basic template for now)
        const iconPath = this.createTrayIcon();
        this.tray = new electron_1.Tray(iconPath);
        this.tray.setToolTip('Nexus - AI-Powered Productivity');
        // On macOS, we need to handle this differently to prevent context menu conflicts
        if (process.platform === 'darwin') {
            // Set up event handlers first, then context menu
            this.setupEventHandlers();
            this.setupContextMenu();
            // Remove context menu initially to prevent auto-popup
            this.tray.setContextMenu(null);
        }
        else {
            this.setupContextMenu();
            this.setupEventHandlers();
        }
    }
    createTrayIcon() {
        // Try to load the menu bar icon - prioritize newer Template images
        const iconPaths = [
            (0, path_1.join)(__dirname, '../assets/tray-iconTemplate.png'),
            (0, path_1.join)(__dirname, '../assets/menubar-icon.png'),
            (0, path_1.join)(process.resourcesPath, 'assets/tray-iconTemplate.png'),
            (0, path_1.join)(process.resourcesPath, 'assets/menubar-icon.png'),
            (0, path_1.join)(__dirname, '../../assets/tray-iconTemplate.png'),
            (0, path_1.join)(__dirname, '../../assets/menubar-icon.png'),
            (0, path_1.join)(__dirname, '../../../assets/tray-iconTemplate.png'),
            (0, path_1.join)(__dirname, '../../../assets/menubar-icon.png')
        ];
        for (const iconPath of iconPaths) {
            try {
                console.log(`Trying to load tray icon from: ${iconPath}`);
                const icon = electron_1.nativeImage.createFromPath(iconPath);
                if (!icon.isEmpty()) {
                    // For template images, set the template flag
                    if (iconPath.includes('Template') || iconPath.includes('menubar-icon')) {
                        icon.setTemplateImage(true);
                    }
                    console.log(`✅ Successfully loaded tray icon from: ${iconPath}`);
                    return icon;
                }
                else {
                    console.log(`❌ Icon was empty: ${iconPath}`);
                }
            }
            catch (error) {
                console.log(`❌ Failed to load icon from ${iconPath}:`, error instanceof Error ? error.message : String(error));
            }
        }
        console.log('⚠️  All tray icon paths failed, creating simple fallback');
        return this.createFallbackIcon();
    }
    createFallbackIcon() {
        try {
            // Fallback: create a simple tray icon using native image API
            const size = 16;
            const buffer = Buffer.alloc(size * size * 4); // RGBA
            // Create a simple filled circle
            for (let y = 0; y < size; y++) {
                for (let x = 0; x < size; x++) {
                    const dx = x - size / 2;
                    const dy = y - size / 2;
                    const distance = Math.sqrt(dx * dx + dy * dy);
                    const index = (y * size + x) * 4;
                    if (distance < 6) {
                        // Inner circle - black
                        buffer[index] = 0; // R
                        buffer[index + 1] = 0; // G
                        buffer[index + 2] = 0; // B
                        buffer[index + 3] = 255; // A
                    }
                    else {
                        // Transparent
                        buffer[index] = 0;
                        buffer[index + 1] = 0;
                        buffer[index + 2] = 0;
                        buffer[index + 3] = 0;
                    }
                }
            }
            const icon = electron_1.nativeImage.createFromBuffer(buffer, { width: size, height: size });
            icon.setTemplateImage(true);
            return icon;
        }
        catch (error) {
            console.error('Failed to create fallback icon:', error);
            // Return a minimal empty image
            return electron_1.nativeImage.createEmpty();
        }
    }
    setupContextMenu() {
        this.contextMenu = electron_1.Menu.buildFromTemplate([
            {
                label: 'Show App',
                click: () => {
                    this.showWindow();
                }
            },
            {
                label: 'Quit',
                click: () => {
                    electron_1.app.quit();
                }
            }
        ]);
        this.tray.setContextMenu(this.contextMenu);
    }
    setupEventHandlers() {
        // On macOS, we need special handling to avoid context menu conflicts
        if (process.platform === 'darwin') {
            // Left click toggles the window
            this.tray.on('click', (event, bounds) => {
                console.log('Tray clicked - toggling window');
                this.toggleWindow();
            });
            // Right-click shows context menu manually
            this.tray.on('right-click', (event, bounds) => {
                console.log('Tray right-clicked - showing context menu');
                this.tray.popUpContextMenu(this.contextMenu);
            });
            // Double-click also toggles window for good measure
            this.tray.on('double-click', () => {
                console.log('Tray double-clicked - toggling window');
                this.toggleWindow();
            });
        }
        else {
            // On other platforms, left click shows context menu, right click toggles
            this.tray.on('click', () => {
                this.tray.popUpContextMenu();
            });
            this.tray.on('right-click', () => {
                this.toggleWindow();
            });
        }
    }
    showWindow() {
        if (this.window) {
            // Restore window if minimized
            if (this.window.isMinimized()) {
                this.window.restore();
            }
            this.window.show();
            this.window.focus();
            // Bring to front on macOS
            if (process.platform === 'darwin') {
                electron_1.app.dock.show();
                // Force the window to the foreground
                electron_1.app.focus({ steal: true });
            }
        }
    }
    hideWindow() {
        if (this.window) {
            this.window.hide();
            // Hide from dock on macOS when hidden
            if (process.platform === 'darwin') {
                electron_1.app.dock.hide();
            }
        }
    }
    toggleWindow() {
        if (this.window) {
            // More reliable window state detection for macOS
            const isVisible = this.window.isVisible();
            const isFocused = this.window.isFocused();
            console.log(`Toggle window - visible: ${isVisible}, focused: ${isFocused}`);
            if (isVisible && isFocused) {
                console.log('Hiding window');
                this.hideWindow();
            }
            else {
                console.log('Showing window');
                this.showWindow();
            }
        }
    }
    destroy() {
        if (this.tray) {
            this.tray.destroy();
        }
    }
}
exports.SimpleTray = SimpleTray;
