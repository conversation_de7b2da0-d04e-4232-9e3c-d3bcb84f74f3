"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.setupIpcHandlers = setupIpcHandlers;
const electron_1 = require("electron");
const logger_1 = require("../utils/logger");
const shortcuts_1 = require("./shortcuts");
function setupIpcHandlers(notesDB, clipboardManager, mainWindow) {
    // Notes operations
    electron_1.ipcMain.handle('notes:create', async (_event, content, title, parentId, isTemporary) => {
        try {
            // If no title provided, generate a smart name
            let finalTitle = title;
            if (!finalTitle) {
                finalTitle = await notesDB.generateSmartNoteName(parentId || null);
            }
            const noteId = await notesDB.createNote(content, finalTitle, parentId, isTemporary);
            return noteId;
        }
        catch (error) {
            logger_1.SafeLogger.error('Error creating note:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle('notes:createFolder', async (_event, name, parentId, isTemporary) => {
        try {
            const folderId = await notesDB.createFolder(name, parentId, isTemporary);
            return folderId;
        }
        catch (error) {
            logger_1.SafeLogger.error('Error creating folder:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle('notes:update', async (_event, id, content, title) => {
        try {
            await notesDB.updateNote(id, content, title);
        }
        catch (error) {
            logger_1.SafeLogger.error('Error updating note:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle('notes:delete', async (_event, id) => {
        try {
            await notesDB.deleteNote(id);
        }
        catch (error) {
            logger_1.SafeLogger.error('Error deleting note:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle('notes:move', async (_event, noteId, newParentId, newSortOrder) => {
        try {
            await notesDB.moveNote(noteId, newParentId, newSortOrder);
        }
        catch (error) {
            logger_1.SafeLogger.error('Error moving note:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle('notes:search', async (_event, query) => {
        try {
            const notes = await notesDB.searchNotes(query);
            return notes;
        }
        catch (error) {
            logger_1.SafeLogger.error('Error searching notes:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle('notes:getAll', async (_event) => {
        try {
            const notes = await notesDB.getAllNotes();
            return notes;
        }
        catch (error) {
            logger_1.SafeLogger.error('Error getting all notes:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle('notes:getById', async (_event, noteId) => {
        try {
            const note = await notesDB.getNoteById(noteId);
            return note;
        }
        catch (error) {
            logger_1.SafeLogger.error('Error getting note by id:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle('notes:getTree', async (_event) => {
        try {
            const tree = await notesDB.getNotesTree();
            return tree;
        }
        catch (error) {
            logger_1.SafeLogger.error('Error getting notes tree:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle('notes:toggleFolder', async (_event, folderId) => {
        try {
            await notesDB.toggleFolder(folderId);
        }
        catch (error) {
            logger_1.SafeLogger.error('Error toggling folder:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle('notes:getPath', async (_event, noteId) => {
        try {
            const path = await notesDB.getNotePath(noteId);
            return path;
        }
        catch (error) {
            logger_1.SafeLogger.error('Error getting note path:', error);
            throw error;
        }
    });
    // Trash operations
    electron_1.ipcMain.handle('trash:getNotes', async (_event) => {
        try {
            const trashedNotes = await notesDB.getTrashedNotes();
            return trashedNotes;
        }
        catch (error) {
            logger_1.SafeLogger.error('Error getting trashed notes:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle('trash:restore', async (_event, noteId) => {
        try {
            await notesDB.restoreNote(noteId);
        }
        catch (error) {
            logger_1.SafeLogger.error('Error restoring note:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle('trash:permanentDelete', async (_event, noteId) => {
        try {
            await notesDB.permanentlyDeleteNote(noteId);
        }
        catch (error) {
            logger_1.SafeLogger.error('Error permanently deleting note:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle('trash:empty', async (_event) => {
        try {
            const deletedCount = await notesDB.emptyTrash();
            return deletedCount;
        }
        catch (error) {
            logger_1.SafeLogger.error('Error emptying trash:', error);
            throw error;
        }
    });
    // Clipboard operations
    electron_1.ipcMain.handle('clipboard:getHistory', async (_event) => {
        try {
            return clipboardManager.getHistory();
        }
        catch (error) {
            logger_1.SafeLogger.error('Error getting clipboard history:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle('clipboard:clear', async (_event) => {
        try {
            clipboardManager.clearHistory();
        }
        catch (error) {
            logger_1.SafeLogger.error('Error clearing clipboard history:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle('clipboard:copy', async (_event, content) => {
        try {
            clipboardManager.copyToClipboard(content);
        }
        catch (error) {
            logger_1.SafeLogger.error('Error copying to clipboard:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle('clipboard:remove', async (_event, id) => {
        try {
            clipboardManager.removeItem(id);
        }
        catch (error) {
            logger_1.SafeLogger.error('Error removing clipboard item:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle('clipboard:search', async (_event, query) => {
        try {
            return clipboardManager.searchHistory(query);
        }
        catch (error) {
            logger_1.SafeLogger.error('Error searching clipboard history:', error);
            throw error;
        }
    });
    // Window operations
    electron_1.ipcMain.handle('window:hide', async (_event) => {
        mainWindow.hide();
    });
    electron_1.ipcMain.handle('window:show', async (_event) => {
        mainWindow.show();
        mainWindow.focus();
    });
    electron_1.ipcMain.handle('window:toggle', async (_event) => {
        if (mainWindow.isVisible() && mainWindow.isFocused()) {
            mainWindow.hide();
        }
        else {
            mainWindow.show();
            mainWindow.focus();
        }
    });
    // App operations
    electron_1.ipcMain.handle('app:quit', async (_event) => {
        require('electron').app.quit();
    });
    // Shortcut operations
    electron_1.ipcMain.handle('shortcuts:getSettings', async (_event) => {
        try {
            const manager = (0, shortcuts_1.getShortcutSettings)();
            return manager.getSettings();
        }
        catch (error) {
            logger_1.SafeLogger.error('Error getting shortcut settings:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle('shortcuts:updateSettings', async (_event, settings) => {
        try {
            const manager = (0, shortcuts_1.getShortcutSettings)();
            manager.updateSettings(settings);
            // Re-register shortcuts with new configuration
            (0, shortcuts_1.updateShortcuts)(mainWindow);
        }
        catch (error) {
            logger_1.SafeLogger.error('Error updating shortcut settings:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle('shortcuts:getAvailable', async (_event) => {
        try {
            const manager = (0, shortcuts_1.getShortcutSettings)();
            return manager.getAvailableShortcuts();
        }
        catch (error) {
            logger_1.SafeLogger.error('Error getting available shortcuts:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle('shortcuts:test', async (_event, shortcut, action) => {
        try {
            // For local shortcuts (like create-new-note), we don't need to test global availability
            const localShortcuts = ['create-new-note', 'create-note'];
            if (action && localShortcuts.includes(action)) {
                // Local shortcuts don't need global registration, so they're always "available"
                return true;
            }
            // For global shortcuts, test if they're available by trying to register temporarily
            const success = electron_1.globalShortcut.register(shortcut, () => { });
            if (success) {
                electron_1.globalShortcut.unregister(shortcut);
            }
            return success;
        }
        catch (error) {
            logger_1.SafeLogger.error('Error testing shortcut:', error);
            return false;
        }
    });
    // Set up database change listeners for real-time updates
    notesDB.on('noteUpdated', (note) => {
        mainWindow.webContents.send('note-updated', note);
    });
    notesDB.on('noteCreated', (note) => {
        mainWindow.webContents.send('note-created', note);
    });
    notesDB.on('noteDeleted', (noteId) => {
        mainWindow.webContents.send('note-deleted', noteId);
    });
    logger_1.SafeLogger.info('IPC handlers setup complete');
}
