"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
const electronAPI = {
    // Notes operations
    createNote: (content, title, parentId, isTemporary) => electron_1.ipcRenderer.invoke('notes:create', content, title, parentId, isTemporary),
    createFolder: (name, parentId, isTemporary) => electron_1.ipcRenderer.invoke('notes:createFolder', name, parentId, isTemporary),
    updateNote: (id, content, title) => electron_1.ipcRenderer.invoke('notes:update', id, content, title),
    deleteNote: (id, deleteChildren) => electron_1.ipcRenderer.invoke('notes:delete', id, deleteChildren),
    moveNote: (noteId, newParentId) => electron_1.ipcRenderer.invoke('notes:move', noteId, newParentId),
    toggleFolder: (folderId) => electron_1.ipcRenderer.invoke('notes:toggleFolder', folderId),
    searchNotes: (query) => electron_1.ipcRenderer.invoke('notes:search', query),
    getAllNotes: () => electron_1.ipcRenderer.invoke('notes:getAll'),
    getNoteById: (noteId) => electron_1.ipcRenderer.invoke('notes:getById', noteId),
    getNotesTree: () => electron_1.ipcRenderer.invoke('notes:getTree'),
    getNotePath: (noteId) => electron_1.ipcRenderer.invoke('notes:getPath', noteId),
    // Trash operations
    getTrashedNotes: () => electron_1.ipcRenderer.invoke('trash:getNotes'),
    restoreNote: (noteId) => electron_1.ipcRenderer.invoke('trash:restore', noteId),
    permanentlyDeleteNote: (noteId) => electron_1.ipcRenderer.invoke('trash:permanentDelete', noteId),
    emptyTrash: () => electron_1.ipcRenderer.invoke('trash:empty'),
    // Clipboard operations
    getClipboardHistory: () => electron_1.ipcRenderer.invoke('clipboard:getHistory'),
    clearClipboardHistory: () => electron_1.ipcRenderer.invoke('clipboard:clear'),
    copyToClipboard: (content) => electron_1.ipcRenderer.invoke('clipboard:copy', content),
    removeClipboardItem: (id) => electron_1.ipcRenderer.invoke('clipboard:remove', id),
    searchClipboardHistory: (query) => electron_1.ipcRenderer.invoke('clipboard:search', query),
    // Window operations
    hideWindow: () => electron_1.ipcRenderer.invoke('window:hide'),
    showWindow: () => electron_1.ipcRenderer.invoke('window:show'),
    toggleWindow: () => electron_1.ipcRenderer.invoke('window:toggle'),
    // App operations
    quit: () => electron_1.ipcRenderer.invoke('app:quit'),
    // Shortcut operations
    getShortcutSettings: () => electron_1.ipcRenderer.invoke('shortcuts:getSettings'),
    updateShortcutSettings: (settings) => electron_1.ipcRenderer.invoke('shortcuts:updateSettings', settings),
    getAvailableShortcuts: () => electron_1.ipcRenderer.invoke('shortcuts:getAvailable'),
    testShortcut: (shortcut, action) => electron_1.ipcRenderer.invoke('shortcuts:test', shortcut, action),
};
// Use `contextBridge` APIs to expose Electron APIs to
// renderer only if context isolation is enabled, otherwise
// just add to the DOM global.
if (process.contextIsolated) {
    try {
        electron_1.contextBridge.exposeInMainWorld('electronAPI', electronAPI);
    }
    catch (error) {
        console.error('Failed to expose electronAPI:', error);
    }
}
else {
    // @ts-ignore (define in dts file)
    window.electronAPI = electronAPI;
}
// Also expose some event listeners for the renderer
electron_1.contextBridge.exposeInMainWorld('electronEvents', {
    // Listen for events from main process
    onCreateNewNote: (callback) => {
        electron_1.ipcRenderer.on('create-new-note', callback);
    },
    onFocusSearch: (callback) => {
        electron_1.ipcRenderer.on('focus-search', callback);
    },
    onSwitchToCalculator: (callback) => {
        electron_1.ipcRenderer.on('switch-to-calculator', callback);
    },
    onSwitchToClipboard: (callback) => {
        electron_1.ipcRenderer.on('switch-to-clipboard', callback);
    },
    onShowPreferences: (callback) => {
        electron_1.ipcRenderer.on('show-preferences', callback);
    },
    onShowShortcutSettings: (callback) => {
        electron_1.ipcRenderer.on('show-shortcut-settings', callback);
    },
    onToggleSidebar: (callback) => {
        electron_1.ipcRenderer.on('toggle-sidebar', callback);
    },
    onSwitchToTrash: (callback) => {
        electron_1.ipcRenderer.on('switch-to-trash', callback);
    },
    // Local shortcut registration
    on: (channel, callback) => {
        electron_1.ipcRenderer.on(channel, callback);
    },
    removeListener: (channel, callback) => {
        electron_1.ipcRenderer.removeListener(channel, callback);
    },
    // Real-time note update events
    onNoteUpdated: (callback) => {
        electron_1.ipcRenderer.on('note-updated', (_event, note) => callback(note));
    },
    onNoteCreated: (callback) => {
        electron_1.ipcRenderer.on('note-created', (_event, note) => callback(note));
    },
    onNoteDeleted: (callback) => {
        electron_1.ipcRenderer.on('note-deleted', (_event, noteId) => callback(noteId));
    },
    // Remove listeners
    removeAllListeners: (channel) => {
        electron_1.ipcRenderer.removeAllListeners(channel);
    },
});
