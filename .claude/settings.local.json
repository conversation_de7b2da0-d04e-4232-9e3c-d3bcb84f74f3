{"permissions": {"allow": ["Bash(npm run type-check:*)", "Bash(npm run lint)", "Bash(timeout 30s npm run dev)", "<PERSON><PERSON>(true)", "Bash(npm run build:main:*)", "Bash(lsof:*)", "<PERSON><PERSON>(curl:*)", "Bash(find:*)", "Bash(npx tsc:*)", "Bash(npm run build:electron:*)", "Bash(npx electron:*)", "Bash(node:*)", "Bash(npm run postinstall:*)", "Bash(rm:*)", "Bash(npm install:*)", "Bash(npm cache clean:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs"], "deny": []}}