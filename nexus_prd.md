# Nexus Application Enhancement PRD

## Overview
This PRD outlines enhancements to the Nexus note-taking application to improve visual consistency, native macOS appearance, and user workflow efficiency. All modifications are supplementary additions and UI refinements that do not affect core functionality.

## 1. Typography Consistency Enhancement

### Requirements
- **Objective**: Unify typography across the application header elements
- **Scope**: Three primary header elements must have consistent typography:
  - Application title "Nexus" (top center)
  - Section title "Notes (Tree)" (left panel header)
  - Note title "Note1" (main content area header)

### Specifications
- All three elements must use identical font family, font size, and font weight
- Vertical alignment: All text baselines must be positioned on the same horizontal centerline
- Current alignment appears correct and should be maintained

## 2. Native macOS Font Integration

### Requirements
- **Objective**: Replace current fonts with macOS system fonts to achieve native appearance
- **Scope**: Application-wide font standardization

### Specifications
- **System Font**: Implement San Francisco (SF) as the primary typeface
  - Header elements: SF Pro Display
  - Body text/notes content: SF Pro Text
  - UI elements: SF Pro Text
- **Font Weights**:
  - Headers: Medium (500) or Semibold (600)
  - Body text: Regular (400)
  - UI elements: Regular (400)
- **Font Sizes**: Follow Apple's Human Interface Guidelines for macOS typography scales

## 3. SF Symbols Icon Optimization

### Requirements
- **Objective**: Increase SF Symbols icon sizes for better visual balance
- **Current State**: Icons appear slightly undersized relative to surrounding UI elements

### Specifications
- Increase current icon sizes by 10-15%
- Maintain consistent scaling across all SF Symbols throughout the application
- Ensure icons remain properly aligned within their containers
- Test icon clarity at different display scales (1x, 2x, 3x)

## 4. System Tray Menu Simplification

### Requirements
- **Objective**: Streamline system tray context menu to essential functions only
- **Current State**: Menu may contain unnecessary options

### Specifications
- **Menu Items** (in order):
  1. "Show App" - Brings application window to foreground
  2. "Quit" - Terminates application completely
- **Functionality Requirements**:
  - "Show App": Must restore window if minimized/hidden and bring to front
  - "Quit": Must properly close all windows and terminate all processes
- **Menu Styling**: Follow macOS system menu appearance standards

## 5. Enhanced Keyboard Shortcuts System

### Requirements
- **Objective**: Expand shortcut configuration with in-app shortcuts category
- **Current State**: Only window toggle shortcut exists

### New Category: "In-App Shortcuts"
#### 5.1 UI Enhancement
- Add new section in Toggle Window Shortcut interface
- **Section Title**: "In-App Shortcuts"
- **Location**: Below existing "Show/Hide Window Shortcut" section
- **Visual Separation**: Use similar styling and spacing as existing sections

#### 5.2 New Shortcut: "Create New Note"
- **Default Binding**: ⌘+N (Command+N)
- **Functionality**: 
  - Trigger only when Nexus application has focus
  - Create new note in current folder/location
  - Immediately focus cursor in note content area for typing
  - Enable instant note-taking workflow
- **User Experience**: 
  - One-keystroke transition from any in-app state to active note editing
  - Support rapid capture of thoughts/information

#### 5.3 Configuration Interface
- **Input Field**: Similar to existing shortcut configuration
- **Change Button**: "Change Shortcut" functionality
- **Reset Button**: Restore to default ⌘+N
- **Validation**: Prevent conflicts with system shortcuts
- **Save/Cancel**: Consistent with existing interface

## Technical Constraints
- All modifications must be supplementary to existing functionality
- No changes to core application logic or data handling
- Maintain backward compatibility with existing user configurations
- Preserve current application performance characteristics

## Acceptance Criteria
1. Typography consistency verified across all three header elements
2. Font rendering matches native macOS applications (tested against Finder, Notes.app)
3. SF Symbols icons display with improved visual weight and clarity
4. System tray menu contains only specified items with verified functionality
5. New shortcut category appears in settings with functional ⌘+N binding
6. New note creation workflow operates seamlessly from any in-app context

## Testing Requirements
- Visual regression testing on macOS 12+ across different display scales
- Keyboard shortcut conflict detection and resolution
- System tray functionality verification across macOS versions
- Font rendering validation on various macOS font rendering settings