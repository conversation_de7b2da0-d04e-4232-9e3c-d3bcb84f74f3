@tailwind base;
@tailwind components;
@tailwind utilities;

/* Save Indicator Animations */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes fadeInOut {
  0% { opacity: 0; transform: scale(0.5); }
  50% { opacity: 1; transform: scale(1); }
  100% { opacity: 0; transform: scale(0.5); }
}

.save-icon-saving {
  animation: pulse 1s infinite;
}

.animate-fade-in-out {
  animation: fadeInOut 1s ease-in-out;
}

/* Resizable Panel Styles */
.main-layout {
  display: flex;
  height: 100vh;
  overflow: hidden;
}

.resizable-handle {
  background: var(--border-color) !important;
  transition: background-color 0.2s ease;
}

.resizable-handle:hover {
  background: var(--accent-color) !important;
}

/* Unified Panel Structure */
.app-layout {
  display: flex;
  height: 100vh;
  align-items: stretch; /* Ensure all panels stretch to full height */
}

.panel-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* Standardized Panel Headers with Fixed Height */
.panel-header {
  height: 48px; /* Fixed height for all headers */
  display: flex;
  align-items: center;
  padding: 0 16px;
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-secondary);
  flex-shrink: 0; /* Prevent header from shrinking */
}

.panel-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.panel-content {
  flex: 1;
  overflow: auto;
}

/* Apply to all three panels */
.sidebar-header,
.notes-tree-header,
.editor-header {
  height: 48px; /* Fixed height for all headers */
  display: flex;
  align-items: center;
  padding: 0 16px;
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-secondary);
  flex-shrink: 0; /* Prevent header from shrinking */
}

.sidebar-header {
  justify-content: space-between; /* Space between title and search button */
}

.sidebar-title,
.notes-tree-title,
.editor-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  line-height: 1.2;
}

.sidebar-content,
.notes-content,
.editor-content {
  flex: 1;
  overflow: auto;
}

/* Sidebar collapse animations with improved transitions */
.sidebar {
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: width;
  overflow: hidden;
}

.sidebar.collapsed {
  width: 60px;
}

.sidebar.collapsed .sidebar-text {
  display: none;
}

/* Splitter improvements */
.splitter {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 1px;
  background: var(--border-color);
  cursor: ew-resize;
  transition: left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 10;
}

.splitter:hover {
  background: var(--accent-color);
  width: 2px;
}

.splitter.resizing {
  background: var(--accent-color);
  width: 2px;
  transition: none; /* Disable transition during resize */
}

/* Theme Variables - macOS Native Colors */
:root {
  /* Light theme (default) - macOS Light Mode */
  --bg-primary: #ffffff;
  --bg-secondary: #f5f5f7;
  --bg-tertiary: #e8e8ed;
  --text-primary: #1d1d1f;
  --text-secondary: #86868b;
  --text-tertiary: #a1a1a6;
  --border-color: #d2d2d7;
  --border-hover: #a1a1a6;
  --accent-color: #007aff;
  --accent-hover: #0056cc;
  --success-color: #34c759;
  --error-color: #ff3b30;
  --warning-color: #ff9500;
}

:root[data-theme="dark"] {
  /* Dark theme - macOS Dark Mode (matching system title bar) */
  --bg-primary: #1e1e1e;
  --bg-secondary: #2d2d30;
  --bg-tertiary: #3c3c3c;
  --text-primary: #ffffff;
  --text-secondary: #cccccc;
  --text-tertiary: #969696;
  --border-color: #434343;
  --border-hover: #555555;
  --accent-color: #0a84ff;
  --accent-hover: #409cff;
  --success-color: #32d74b;
  --error-color: #ff453a;
  --warning-color: #ff9f0a;
}

@media (prefers-color-scheme: dark) {
  :root:not([data-theme="light"]) {
    /* Auto dark theme when system is dark and no explicit light theme */
    --bg-primary: #1e1e1e;
    --bg-secondary: #2d2d30;
    --bg-tertiary: #3c3c3c;
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --text-tertiary: #969696;
    --border-color: #434343;
    --border-hover: #555555;
    --accent-color: #0a84ff;
    --accent-hover: #409cff;
    --success-color: #32d74b;
    --error-color: #ff453a;
    --warning-color: #ff9f0a;
  }
}

/* Custom styles for Nexus */
@layer base {
  * {
    box-sizing: border-box;
  }
  
  /* San Francisco Font System */
  h1, h2, h3, h4, h5, h6,
  .header-font,
  .sidebar-title,
  .notes-tree-title,
  .editor-title {
    font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    font-weight: 600; /* Semibold */
  }
  
  /* Ensure proper inheritance */
  html, body {
    font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    transition: background-color 0.3s ease, color 0.3s ease;
  }
  
  #root {
    background-color: var(--bg-primary);
    color: var(--text-primary);
    min-height: 100vh;
  }
  
  /* Global theme overrides for all components */
  .bg-white {
    background-color: var(--bg-primary) !important;
  }
  
  .bg-gray-50 {
    background-color: var(--bg-secondary) !important;
  }
  
  .bg-gray-100 {
    background-color: var(--bg-secondary) !important;
  }
  
  .bg-gray-200 {
    background-color: var(--bg-tertiary) !important;
  }
  
  .text-gray-900 {
    color: var(--text-primary) !important;
  }
  
  .text-gray-800 {
    color: var(--text-primary) !important;
  }
  
  .text-gray-700 {
    color: var(--text-primary) !important;
  }
  
  .text-gray-600 {
    color: var(--text-secondary) !important;
  }
  
  .text-gray-500 {
    color: var(--text-secondary) !important;
  }
  
  .text-gray-400 {
    color: var(--text-tertiary) !important;
  }
  
  .border-gray-200 {
    border-color: var(--border-color) !important;
  }
  
  .border-gray-300 {
    border-color: var(--border-color) !important;
  }
  
  .border-l {
    border-left-color: var(--border-color) !important;
  }
  
  .border-r {
    border-right-color: var(--border-color) !important;
  }
  
  .border-t {
    border-top-color: var(--border-color) !important;
  }
  
  .border-b {
    border-bottom-color: var(--border-color) !important;
  }
  
  .hover\:bg-gray-50:hover {
    background-color: var(--bg-secondary) !important;
  }
  
  .hover\:bg-gray-100:hover {
    background-color: var(--bg-tertiary) !important;
  }
  
  .hover\:text-gray-900:hover {
    color: var(--text-primary) !important;
  }
  
  .hover\:text-gray-700:hover {
    color: var(--text-primary) !important;
  }
  
  /* Force all containers to use theme colors */
  * {
    scrollbar-color: var(--text-tertiary) var(--bg-secondary);
  }
  
  /* Specific overrides for common container patterns */
  div[class*="bg-"], 
  section[class*="bg-"],
  main[class*="bg-"],
  nav[class*="bg-"],
  aside[class*="bg-"] {
    background-color: var(--bg-primary);
  }
  
  /* Input and form elements */
  input, textarea, select {
    background-color: var(--bg-primary) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
  }
  
  input:focus, textarea:focus, select:focus {
    border-color: var(--accent-color) !important;
  }
  
  /* SPECIFIC OVERRIDE for CodeMirror textarea - HIGHER SPECIFICITY */
  .CodeMirror textarea {
    background-color: transparent !important;
    color: transparent !important;
    border: none !important;
    border-color: transparent !important;
    outline: none !important;
    caret-color: var(--text-primary) !important;
  }
  
  .CodeMirror textarea:focus {
    background-color: transparent !important;
    color: transparent !important;
    border: none !important;
    border-color: transparent !important;
    outline: none !important;
    box-shadow: none !important;
  }
}

@layer components {
  /* Theme-aware utility classes */
  .theme-bg-primary {
    background-color: var(--bg-primary);
  }
  
  .theme-bg-secondary {
    background-color: var(--bg-secondary);
  }
  
  .theme-bg-tertiary {
    background-color: var(--bg-tertiary);
  }
  
  .theme-text-primary {
    color: var(--text-primary);
  }
  
  .theme-text-secondary {
    color: var(--text-secondary);
  }
  
  .theme-text-tertiary {
    color: var(--text-tertiary);
  }
  
  .theme-border {
    border-color: var(--border-color);
  }
  
  .theme-accent {
    color: var(--accent-color);
  }
  
  .theme-accent-bg {
    background-color: var(--accent-color);
  }
  
  /* Input styling */
  .input-theme {
    background-color: var(--bg-primary);
    color: var(--text-primary);
    border-color: var(--border-color);
    transition: border-color 0.2s ease;
  }
  
  .input-theme:focus {
    border-color: var(--accent-color);
    outline: none;
  }
  
  .input-theme:hover {
    border-color: var(--border-hover);
  }
  
  /* Button styling */
  .btn-theme-primary {
    background-color: var(--accent-color);
    color: white;
    transition: background-color 0.2s ease;
  }
  
  .btn-theme-primary:hover {
    background-color: var(--accent-hover);
  }
  
  .btn-theme-secondary {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    transition: all 0.2s ease;
  }
  
  .btn-theme-secondary:hover {
    background-color: var(--bg-tertiary);
    border-color: var(--border-hover);
  }
  
  /* Custom scrollbar */
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
  }
  
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(156, 163, 175, 0.5);
    border-radius: 3px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: rgba(156, 163, 175, 0.7);
  }

  /* Button styles */
  .btn-primary {
    @apply bg-nexus-500 hover:bg-nexus-600 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 shadow-sm hover:shadow;
  }
  
  .btn-secondary {
    @apply bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2 px-4 rounded-lg transition-all duration-200 border border-gray-200 hover:border-gray-300;
  }
  
  .btn-ghost {
    @apply hover:bg-gray-100 text-gray-600 hover:text-gray-800 font-medium py-2 px-3 rounded-lg transition-all duration-200;
  }

  /* Input styles */
  .input-primary {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-nexus-500 focus:border-transparent transition-all duration-200;
  }
  
  .input-search {
    @apply w-full px-4 py-2.5 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-nexus-500 focus:border-transparent focus:bg-white placeholder-gray-400 transition-all duration-200 text-sm;
  }

  /* Card styles */
  .card {
    @apply bg-white rounded-xl shadow-sm border border-gray-100;
  }
  
  .card-hover {
    @apply card hover:shadow-md hover:border-gray-200 transition-all duration-200 cursor-pointer;
  }

  /* Note item styles */
  .note-item {
    @apply p-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer transition-colors duration-150;
  }
  
  .note-item:last-child {
    @apply border-b-0;
  }
  
  .note-item.selected {
    @apply bg-nexus-50 border-l-4 border-l-nexus-500;
  }

  /* Tree node styles */
  .newly-created {
    @apply bg-blue-50 border-l-4 border-l-blue-400 shadow-sm;
    animation: slideInFromLeft 0.3s ease-out;
  }

  .temporary-item {
    @apply bg-yellow-50 border-l-2 border-l-yellow-400 opacity-80;
  }

  .editing-item {
    @apply bg-blue-50 border-l-4 border-l-blue-600 shadow-md;
  }

  /* Animation for newly created items */
  @keyframes slideInFromLeft {
    from {
      opacity: 0;
      transform: translateX(-10px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  /* Inline edit input styling */
  .inline-edit-input {
    @apply border border-blue-500 rounded px-2 py-1 text-sm bg-white outline-none;
    @apply focus:border-blue-600 focus:ring-1 focus:ring-blue-500;
    @apply w-full min-w-24 select-text;
  }

  /* Editor styles */
  .editor-textarea {
    @apply w-full h-full resize-none border-none outline-none bg-transparent text-sm leading-relaxed;
    font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  }

  /* Markdown Preview styles */
  .markdown-preview {
    font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    line-height: 1.6;
  }

  .markdown-preview h1,
  .markdown-preview h2,
  .markdown-preview h3,
  .markdown-preview h4,
  .markdown-preview h5,
  .markdown-preview h6 {
    font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    color: var(--text-primary);
    font-weight: 600;
    margin-top: 24px;
    margin-bottom: 12px;
    line-height: 1.25;
  }

  .markdown-preview h1 { font-size: 2em; }
  .markdown-preview h2 { font-size: 1.5em; }
  .markdown-preview h3 { font-size: 1.25em; }
  .markdown-preview h4 { font-size: 1.1em; }
  .markdown-preview h5 { font-size: 1em; }
  .markdown-preview h6 { font-size: 0.9em; }

  .markdown-preview p {
    margin-bottom: 16px;
    color: var(--text-primary);
  }

  .markdown-preview ul,
  .markdown-preview ol {
    margin-bottom: 16px;
    padding-left: 20px;
  }

  .markdown-preview li {
    margin-bottom: 4px;
    color: var(--text-primary);
  }

  .markdown-preview blockquote {
    border-left: 4px solid var(--accent-color);
    padding-left: 16px;
    margin: 16px 0;
    color: var(--text-secondary);
    font-style: italic;
  }

  .markdown-preview code {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
    font-size: 0.85em;
  }

  .markdown-preview pre {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    padding: 16px;
    border-radius: 6px;
    overflow-x: auto;
    margin: 16px 0;
  }

  .markdown-preview pre code {
    background: transparent;
    padding: 0;
  }

  .markdown-preview a {
    color: var(--accent-color);
    text-decoration: none;
  }

  .markdown-preview a:hover {
    text-decoration: underline;
  }

  .markdown-preview table {
    border-collapse: collapse;
    width: 100%;
    margin: 16px 0;
  }

  .markdown-preview th,
  .markdown-preview td {
    border: 1px solid var(--border-color);
    padding: 8px 12px;
    text-align: left;
  }

  .markdown-preview th {
    background-color: var(--bg-secondary);
    font-weight: 600;
  }

  .markdown-preview hr {
    border: none;
    height: 1px;
    background-color: var(--border-color);
    margin: 24px 0;
  }

  .markdown-preview img {
    max-width: 100%;
    height: auto;
    margin: 16px 0;
    border-radius: 6px;
  }

  /* Sidebar styles */
  .sidebar {
    @apply w-64 bg-white border-r border-gray-200 flex flex-col;
  }
  
  /* Removed old sidebar-header definition - using unified header styles above */
  
  .sidebar-content {
    @apply flex-1 overflow-y-auto custom-scrollbar;
  }

  /* Navigation styles */
  .nav-item {
    @apply flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors duration-150;
  }
  
  .nav-item.active {
    @apply text-nexus-600 bg-nexus-50;
  }

  /* Utility classes */
  .text-truncate {
    @apply truncate;
  }
  
  .text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .text-truncate-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

@layer utilities {
  /* Hide scrollbar but keep functionality */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  /* Theme-aware component styles */
  .theme-bg-primary {
    background-color: var(--bg-primary);
  }
  
  .theme-bg-secondary {
    background-color: var(--bg-secondary);
  }
  
  .theme-bg-tertiary {
    background-color: var(--bg-tertiary);
  }
  
  .theme-text-primary {
    color: var(--text-primary);
  }
  
  .theme-text-secondary {
    color: var(--text-secondary);
  }
  
  .theme-text-tertiary {
    color: var(--text-tertiary);
  }
  
  .theme-border {
    border-color: var(--border-color);
  }
  
  .theme-border-hover:hover {
    border-color: var(--border-hover);
  }
  
  .theme-accent {
    color: var(--accent-color);
  }
  
  .theme-accent-bg {
    background-color: var(--accent-color);
  }
  
  .theme-accent-hover:hover {
    background-color: var(--accent-hover);
  }

  /* Prevent text selection */
  .select-none {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
  
  /* macOS-style backdrop blur */
  .backdrop-blur-macos {
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
  }
}
