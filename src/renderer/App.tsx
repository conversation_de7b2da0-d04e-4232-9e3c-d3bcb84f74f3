import React, { useState, useEffect } from 'react';
import { Resizable } from 're-resizable';
import { Sidebar } from './components/Sidebar';
import { TreeSidebar } from './components/TreeSidebar';
import { NoteEditor } from './components/NoteEditor';
import { Calculator } from './components/Calculator';
import { ClipboardView } from './components/ClipboardView';
import { UnitConverter } from './components/UnitConverter';
import { Translator } from './components/Translator';
import { TrashView } from './components/TrashView';
import { WelcomeModal } from './components/WelcomeModal';
import { SearchSpotlight } from './components/SearchSpotlight';
import { useAppStore, setupRealTimeUpdates } from './store/appStore';
import { useSystemTheme } from './hooks/useSystemTheme';
import { useResponsiveLayout } from './hooks/useResponsiveLayout';
import { Note } from '../shared/types';

type ViewType = 'notes' | 'translator' | 'calculator' | 'clipboard' | 'converter' | 'trash';

function App() {
  const [currentView, setCurrentView] = useState<ViewType>('notes');
  const [sidebarWidth, setSidebarWidth] = useState(256); // 16rem in pixels
  const [middlePaneWidth, setMiddlePaneWidth] = useState(320); // Middle pane width
  const [showWelcome, setShowWelcome] = useState(false);
  const [showSearchSpotlight, setShowSearchSpotlight] = useState(false);
  // Removed sidebar collapse functionality - sidebar is always visible
  const { selectedNote, setSelectedNote } = useAppStore();
  // Removed refs for manual resize handling - now using re-resizable library
  
  // Theme management
  const { theme: currentTheme, toggleTheme } = useSystemTheme();
  
  // Responsive layout management
  const { windowSize, getOptimalPanelWidths } = useResponsiveLayout();


  // Listen for events from main process
  useEffect(() => {
    if (window.electronEvents) {
      // Handle create new note
      window.electronEvents.onCreateNewNote(() => {
        setCurrentView('notes');
        setSelectedNote(null);
        // Focus will be handled by the NotesView component
      });

      // Handle focus search - now opens search spotlight
      window.electronEvents.onFocusSearch(() => {
        setCurrentView('notes');
        setShowSearchSpotlight(true);
      });

      // Handle switch to calculator
      window.electronEvents.onSwitchToCalculator(() => {
        setCurrentView('calculator');
      });

      // Handle switch to clipboard
      window.electronEvents.onSwitchToClipboard(() => {
        setCurrentView('clipboard');
      });
      
      // Handle switch to trash
      window.electronEvents.onSwitchToTrash(() => {
        setCurrentView('trash');
      });
      
      
      // Toggle sidebar functionality removed

      // Handle show preferences
      window.electronEvents.onShowPreferences(() => {
        // TODO: Implement preferences view
        console.log('Show preferences requested');
      });

      // Cleanup listeners on unmount
      return () => {
        window.electronEvents.removeAllListeners('create-new-note');
        window.electronEvents.removeAllListeners('focus-search');
        window.electronEvents.removeAllListeners('switch-to-calculator');
        window.electronEvents.removeAllListeners('switch-to-clipboard');
        window.electronEvents.removeAllListeners('switch-to-trash');
        // Toggle sidebar listener removed
        window.electronEvents.removeAllListeners('show-preferences');
      };
    }
  }, [setSelectedNote]);

  // Set up real-time updates
  useEffect(() => {
    setupRealTimeUpdates();
  }, []);

  // Global keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault();
        setCurrentView('notes');
        setShowSearchSpotlight(true);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  // Check if this is the first time using the app
  useEffect(() => {
    const hasSeenWelcome = localStorage.getItem('nexus-has-seen-welcome');
    if (!hasSeenWelcome) {
      // Show welcome after a short delay to let the app fully load
      const timer = setTimeout(() => {
        setShowWelcome(true);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, []);

  // Debug layout state
  useEffect(() => {
    console.log('Layout state:', {
      sidebarWidth,
      middlePaneWidth,
      windowWidth: windowSize.width
    });
  }, [sidebarWidth, middlePaneWidth, windowSize.width]);

  // Adjust panel widths on window resize
  useEffect(() => {
    const optimalWidths = getOptimalPanelWidths(windowSize.width, false); // Always not collapsed
    
    // Only adjust if the current widths are significantly different from optimal
    const sidebarDiff = Math.abs(sidebarWidth - optimalWidths.sidebar);
    const notesDiff = Math.abs(middlePaneWidth - optimalWidths.notes);
    
    if (sidebarDiff > 50 || notesDiff > 100) {
      setSidebarWidth(optimalWidths.sidebar);
      setMiddlePaneWidth(optimalWidths.notes);
    }
  }, [windowSize.width, getOptimalPanelWidths, sidebarWidth, middlePaneWidth]);

  // Handle sidebar resizing
  // Manual resize handlers removed - using re-resizable library for clean resize handling

  const renderTreeSidebar = () => {
    return (
      <TreeSidebar
        searchQuery=""
        selectedNodeId={selectedNote?.id}
        onNodeSelect={(node) => {
          const note: Note = {
            id: node.id,
            content: node.content,
            title: node.title || undefined,
            created_at: node.createdAt.toISOString(),
            updated_at: node.updatedAt.toISOString(),
            parent_id: node.parentId,
            is_folder: node.isFolder,
            is_expanded: node.isExpanded,
            is_pinned: node.isPinned
          };
          setSelectedNote(note);
        }}
        onCreateNote={async (parentId) => {
          try {
            if (window.electronAPI) {
              const noteId = await window.electronAPI.createNote('', '', parentId);
              console.log('Created note with ID:', noteId);
            }
          } catch (error) {
            console.error('Error creating note:', error);
          }
        }}
        onCreateFolder={async (parentId) => {
          try {
            if (window.electronAPI) {
              const folderId = await window.electronAPI.createFolder('New Folder', parentId, true);
              console.log('Created folder with ID:', folderId);
            }
          } catch (error) {
            console.error('Error creating folder:', error);
          }
        }}
      />
    );
  };

  const renderEditor = () => {
    if (!selectedNote || selectedNote.is_folder) {
      return (
        <div 
          className="flex-1 flex items-center justify-center"
          style={{ 
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            textAlign: 'center',
            padding: '40px'
          }}
        >
          <div 
            className="empty-state-icon"
            style={{
              fontSize: '48px',
              marginBottom: '16px',
              opacity: 0.6
            }}
          >
            <svg width="55" height="55" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" style={{ opacity: 0.6 }}>
              <path d="M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8z"/>
              <polyline points="14,2 14,8 20,8"/>
              <line x1="16" y1="13" x2="8" y2="13"/>
              <line x1="16" y1="17" x2="8" y2="17"/>
              <polyline points="10,9 9,9 8,9"/>
            </svg>
          </div>
          <h2 
            className="empty-state-title"
            style={{
              fontSize: '24px',
              fontWeight: '600',
              color: 'var(--text-primary)',
              margin: '0 0 8px 0'
            }}
          >
            Welcome to Nexus
          </h2>
          <p 
            className="empty-state-description"
            style={{
              fontSize: '14px',
              color: 'var(--text-secondary)',
              margin: '0 0 24px 0',
              lineHeight: '1.5'
            }}
          >
            Select a note from the sidebar to start editing
          </p>
          <div 
            className="empty-state-shortcuts"
            style={{
              display: 'flex',
              flexDirection: 'column',
              gap: '8px',
              alignItems: 'center'
            }}
          >
            <div 
              className="shortcut-item"
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '12px',
                fontSize: '12px',
                color: 'var(--text-secondary)'
              }}
            >
              <kbd style={{
                background: 'var(--bg-secondary)',
                border: '1px solid var(--border-color)',
                borderRadius: '4px',
                padding: '2px 6px',
                fontSize: '11px',
                fontFamily: 'monospace'
              }}>⌘+Shift+Space</kbd>
              <span>Show/hide window</span>
            </div>
          </div>
        </div>
      );
    }

    return (
      <NoteEditor
        note={selectedNote}
        onUpdateNote={async (id, content, title) => {
          try {
            if (window.electronAPI) {
              await window.electronAPI.updateNote(id, content, title);
            }
          } catch (error) {
            console.error('Error updating note:', error);
          }
        }}
      />
    );
  };

  const renderCurrentView = () => {
    switch (currentView) {
      case 'translator':
        return <Translator />;
      case 'calculator':
        return <Calculator />;
      case 'clipboard':
        return <ClipboardView searchQuery="" />;
      case 'converter':
        return <UnitConverter />;
      case 'trash':
        return <TrashView searchQuery="" />;
      default:
        return null; // Notes view is handled separately
    }
  };

  // handleCloseWindow removed - using native macOS window controls only

  return (
    <div className="h-screen flex flex-col" style={{ backgroundColor: 'var(--bg-secondary)' }}>

      {/* Main Content */}
      <div className="flex flex-1 overflow-hidden app-layout">
        {/* Left Sidebar - Always visible */}
        <div style={{ width: sidebarWidth, height: '100%' }}>
          <div className="panel-container sidebar"
               style={{ 
                 width: '100%',
                 overflow: 'hidden',
                 backgroundColor: 'var(--bg-primary)',
                 borderRight: '1px solid var(--border-color)'
               }}>
            <Sidebar
              currentView={currentView}
              onViewChange={setCurrentView}
              onOpenSearch={() => setShowSearchSpotlight(true)}
              theme={currentTheme}
              onThemeToggle={toggleTheme}
            />
          </div>
        </div>

        {/* Main Content Area */}
        {currentView === 'notes' ? (
          <>
            {/* Middle Pane - Tree Sidebar */}
            <Resizable
              size={{ width: middlePaneWidth, height: '100%' }}
              minWidth={250}
              maxWidth={500}
              enable={{ right: true }}
              onResizeStop={(e, direction, ref, d) => {
                setMiddlePaneWidth(middlePaneWidth + d.width);
              }}
              handleStyles={{
                right: {
                  background: 'var(--border-color)',
                  width: '2px',
                  cursor: 'ew-resize',
                  transition: 'background-color 0.2s ease'
                }
              }}
              handleClasses={{
                right: 'resizable-handle'
              }}
            >
              <div className="panel-container" style={{
                backgroundColor: 'var(--bg-primary)'
              }}>
                <div className="panel-content">
                  {renderTreeSidebar()}
                </div>
              </div>
            </Resizable>
            
            {/* Right Pane - Editor */}
            <div className="flex-1 overflow-hidden panel-container editor-panel">
              <div className="panel-content">
                {renderEditor()}
              </div>
            </div>
          </>
        ) : (
          /* Other Views - Full Width */
          <div className="flex-1 overflow-hidden">
            {renderCurrentView()}
          </div>
        )}
      </div>
      
      {/* Welcome Modal */}
      <WelcomeModal 
        isOpen={showWelcome}
        onClose={() => {
          setShowWelcome(false);
          localStorage.setItem('nexus-has-seen-welcome', 'true');
        }}
      />
      
      {/* Search Spotlight */}
      <SearchSpotlight 
        isOpen={showSearchSpotlight}
        onClose={() => setShowSearchSpotlight(false)}
        onSelectNote={(note) => {
          setSelectedNote(note);
          setCurrentView('notes');
          setShowSearchSpotlight(false);
        }}
      />
    </div>
  );
}

export default App;
