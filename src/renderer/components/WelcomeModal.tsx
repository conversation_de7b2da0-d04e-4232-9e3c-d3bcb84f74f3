import { useState, useEffect } from 'react';

interface WelcomeModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function WelcomeModal({ isOpen, onClose }: WelcomeModalProps) {
  const [currentStep, setCurrentStep] = useState(0);

  const steps = [
    {
      title: "Welcome to Nexus!",
      content: (
        <div className="text-center">
          <div className="mb-4">
            <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" className="mx-auto text-blue-500">
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
          </div>
          <p className="text-lg mb-4" style={{ color: 'var(--text-primary)' }}>
            A simple and efficient note-taking tool
          </p>
          <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>
            Let's learn the essential keyboard shortcut
          </p>
        </div>
      )
    },
    {
      title: "Essential Shortcut",
      content: (
        <div className="text-center">
          <div className="mb-6">
            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" className="mx-auto text-gray-600">
              <rect x="2" y="3" width="20" height="14" rx="2" ry="2"/>
              <line x1="8" y1="21" x2="16" y2="21"/>
              <line x1="12" y1="17" x2="12" y2="21"/>
            </svg>
          </div>
          <div className="space-y-4">
            <div className="p-4 rounded-lg" style={{ backgroundColor: 'var(--bg-tertiary)' }}>
              <h3 className="text-lg font-semibold mb-3" style={{ color: 'var(--accent-color)' }}>
                Show/Hide Nexus
              </h3>
              <kbd className="px-4 py-2 rounded-lg text-lg font-mono" style={{ backgroundColor: 'var(--bg-secondary)', color: 'var(--text-primary)', border: '1px solid var(--border-color)' }}>
                ⌘⇧Space
              </kbd>
              <p className="text-sm mt-3" style={{ color: 'var(--text-secondary)' }}>
                Press this shortcut anywhere to toggle Nexus visibility
              </p>
            </div>
            <div className="text-sm text-center space-y-2" style={{ color: 'var(--text-secondary)' }}>
              <p className="flex items-center justify-center gap-2">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <circle cx="12" cy="12" r="10"/>
                  <path d="M9.09,9A3,3,0,0,1,15,9"/>
                  <line x1="12" y1="17" x2="12.01" y2="17"/>
                </svg>
                Nexus runs in the background even when closed
              </p>
              <p className="flex items-center justify-center gap-2">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M6 2L3 6v14a2 2 0 002 2h14a2 2 0 002-2V6l-3-4z"/>
                  <line x1="3" y1="6" x2="21" y2="6"/>
                  <path d="M16 10a4 4 0 01-8 0"/>
                </svg>
                Use the shortcut to access it instantly
              </p>
            </div>
          </div>
        </div>
      )
    }
  ];

  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      onClose();
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  if (!isOpen) return null;

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      style={{ backdropFilter: 'blur(4px)' }}
    >
      <div 
        className="rounded-lg shadow-xl max-w-md w-full mx-4 p-6"
        style={{ backgroundColor: 'var(--bg-primary)', border: '1px solid var(--border-color)' }}
      >
        {/* Header */}
        <div className="text-center mb-6">
          <h2 className="text-xl font-bold mb-2" style={{ color: 'var(--text-primary)' }}>
            {steps[currentStep].title}
          </h2>
          <div className="flex justify-center space-x-2">
            {steps.map((_, index) => (
              <div
                key={index}
                className="w-2 h-2 rounded-full"
                style={{
                  backgroundColor: index === currentStep ? 'var(--accent-color)' : 'var(--border-color)'
                }}
              />
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="mb-8 min-h-[300px] flex items-center justify-center">
          <div className="w-full">
            {steps[currentStep].content}
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-between">
          <button
            onClick={prevStep}
            disabled={currentStep === 0}
            className="px-4 py-2 rounded-lg text-sm font-medium transition-colors"
            style={{
              backgroundColor: currentStep === 0 ? 'transparent' : 'var(--bg-secondary)',
              color: currentStep === 0 ? 'var(--text-tertiary)' : 'var(--text-primary)',
              cursor: currentStep === 0 ? 'not-allowed' : 'pointer'
            }}
          >
            Previous
          </button>

          <button
            onClick={nextStep}
            className="px-6 py-2 rounded-lg text-sm font-medium text-white transition-colors"
            style={{ backgroundColor: 'var(--accent-color)' }}
            onMouseEnter={(e) => {
              (e.target as HTMLElement).style.backgroundColor = 'var(--accent-hover)';
            }}
            onMouseLeave={(e) => {
              (e.target as HTMLElement).style.backgroundColor = 'var(--accent-color)';
            }}
          >
            {currentStep === steps.length - 1 ? 'Get Started' : 'Next'}
          </button>
        </div>
      </div>
    </div>
  );
}