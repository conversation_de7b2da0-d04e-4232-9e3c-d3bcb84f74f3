import React, { useMemo } from 'react';
import MarkdownIt from 'markdown-it';

interface MarkdownPreviewProps {
  content: string;
  className?: string;
  style?: React.CSSProperties;
}

export function MarkdownPreview({ content, className = '', style = {} }: MarkdownPreviewProps) {
  // Initialize markdown-it with reasonable settings
  const md = useMemo(() => {
    return new MarkdownIt({
      html: false,        // Disable HTML tags for security
      xhtmlOut: true,     // Use '/' to close single tags (<br />)
      breaks: true,       // Convert '\n' in paragraphs into <br>
      linkify: true,      // Autoconvert URL-like text to links
      typographer: true,  // Enable some language-neutral replacement + quotes beautification
    });
  }, []);

  // Render markdown to HTML
  const htmlContent = useMemo(() => {
    if (!content || content.trim().length === 0) {
      return '<p style="color: var(--text-secondary); font-style: italic;">No content to preview</p>';
    }
    
    try {
      return md.render(content);
    } catch (error) {
      console.error('Error rendering markdown:', error);
      return '<p style="color: var(--error-color);">Error rendering markdown preview</p>';
    }
  }, [content, md]);

  return (
    <div 
      className={`markdown-preview ${className}`}
      style={{
        padding: '16px',
        color: 'var(--text-primary)',
        backgroundColor: 'transparent',
        height: '100%',
        width: '100%',
        overflow: 'auto',
        overflowX: 'hidden', // Prevent horizontal scroll
        paddingBottom: '40px', // Match the editor padding for footer
        boxSizing: 'border-box',
        position: 'relative',
        flex: 1,
        minHeight: 0, // Allow flex item to shrink
        ...style
      }}
      dangerouslySetInnerHTML={{ __html: htmlContent }}
    />
  );
}