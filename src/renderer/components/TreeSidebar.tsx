import React, { useEffect, useState, useCallback, useMemo } from 'react';
import { NoteNode, SearchResult } from '../../shared/types';
import { TreeNode } from './TreeNode';
import { useAppStore } from '../store/appStore';

interface TreeSidebarProps {
  searchQuery: string;
  selectedNodeId?: string;
  onNodeSelect: (node: NoteNode) => void;
  onCreateNote?: (parentId?: string) => void;
  onCreateFolder?: (parentId?: string) => void;
}

export function TreeSidebar({
  searchQuery,
  selectedNodeId,
  onNodeSelect,
  onCreateNote,
  onCreateFolder
}: TreeSidebarProps) {
  // Use Zustand store instead of local state for tree data
  const {
    notesTree,
    isLoadingTree,
    loadNotesTree,
    createNote,
    moveNote,
    searchResults,
    setSearchResults
  } = useAppStore();

  const [isSearching, setIsSearching] = useState(false);
  const [editingNodeId, setEditingNodeId] = useState<string | null>(null);
  const [newlyCreatedNodeId, setNewlyCreatedNodeId] = useState<string | null>(null);

  // Helper function to find a node in the tree
  const findNodeInTree = useCallback((nodeId: string) => {
    const searchInNodes = (nodes: NoteNode[]): NoteNode | null => {
      for (const node of nodes) {
        if (node.id === nodeId) return node;
        if (node.children.length > 0) {
          const found = searchInNodes(node.children);
          if (found) return found;
        }
      }
      return null;
    };
    return searchInNodes(notesTree);
  }, [notesTree]);

  // Search notes with debouncing - memoized to prevent infinite re-renders
  const searchNotes = useCallback(async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      setIsSearching(false);
      return;
    }

    try {
      setIsSearching(true);
      const results = await window.electronAPI.searchNotes(query);
      setSearchResults(results);
    } catch (error) {
      console.error('Error searching notes:', error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  }, [setSearchResults]);

  // Load tree on mount - only once
  useEffect(() => {
    console.log('TreeSidebar mounted, loading tree...');
    console.log('window.electronAPI available on mount:', !!window.electronAPI);
    loadNotesTree();
  }, []); // Empty dependency array to run only once

  // Handle search with debouncing - stable dependencies
  useEffect(() => {
    if (!searchQuery.trim()) {
      setSearchResults([]);
      setIsSearching(false);
      return;
    }

    setIsSearching(true);
    const searchTimer = setTimeout(() => {
      searchNotes(searchQuery);
    }, 150); // 150ms debounce for responsive feel

    return () => {
      clearTimeout(searchTimer);
    };
  }, [searchQuery]); // Remove searchNotes from dependencies to prevent infinite loop

  const handleToggleFolder = useCallback(async (folderId: string) => {
    try {
      await window.electronAPI.toggleFolder(folderId);
      // Don't reload tree - let real-time updates handle it
    } catch (error) {
      console.error('Error toggling folder:', error);
    }
  }, []);

  // Instant note creation with inline editing - use store method
  const handleCreateNote = useCallback(async (parentId?: string) => {
    console.log('handleCreateNote called with parentId:', parentId);

    if (!window.electronAPI) {
      console.error('window.electronAPI is not available - running in browser mode?');
      alert('Electron API not available. Please run the app in Electron mode.');
      return;
    }

    try {
      console.log('About to call createNote from store...');
      // Use store method which handles both IPC and state updates
      // Pass empty string for title to trigger smart naming
      const noteId = await createNote('', '', parentId);
      console.log('createNote returned noteId:', noteId);

      // Use setTimeout to ensure the tree has been rendered before setting edit state
      setTimeout(() => {
        // Mark as newly created for animation
        setNewlyCreatedNodeId(noteId);
        setTimeout(() => setNewlyCreatedNodeId(null), 2000);

        // Enter edit mode for the new note
        setEditingNodeId(noteId);
        console.log('Edit mode set for nodeId:', noteId);
      }, 100); // Slightly longer delay to ensure tree rendering

      console.log('Created new temporary note for immediate editing:', noteId);
      console.log('Current tree state:', notesTree.length, 'items');
    } catch (error) {
      console.error('Error creating note:', error);
      console.error('Error details:', error instanceof Error ? error.message : 'Unknown error', error instanceof Error ? error.stack : '');
    }
  }, [createNote]);

  // Instant folder creation with inline editing
  const handleCreateFolder = useCallback(async (parentId?: string) => {
    console.log('handleCreateFolder called with parentId:', parentId);

    if (!window.electronAPI) {
      console.error('window.electronAPI is not available - running in browser mode?');
      alert('Electron API not available. Please run the app in Electron mode.');
      return;
    }

    try {
      console.log('About to call window.electronAPI.createFolder...');
      // Create folder immediately with temporary flag
      const folderId = await window.electronAPI.createFolder('New Folder', parentId, true);
      console.log('createFolder returned folderId:', folderId);

      // Reload tree to get the new folder
      console.log('About to reload tree...');
      await loadNotesTree();
      console.log('Tree reloaded');
      console.log('Current tree state after folder creation:', notesTree.length, 'items');
      console.log('Tree data:', notesTree);

      // Use setTimeout to ensure the tree has been rendered before setting edit state
      setTimeout(() => {
        // Mark as newly created for animation
        setNewlyCreatedNodeId(folderId);
        setTimeout(() => setNewlyCreatedNodeId(null), 2000);

        // Enter edit mode for the new folder
        setEditingNodeId(folderId);
        console.log('Edit mode set for folderId:', folderId);
        console.log('Current editingNodeId state:', folderId);
        console.log('Current newlyCreatedNodeId state:', folderId);
      }, 100); // Slightly longer delay to ensure tree rendering

    } catch (error) {
      console.error('Error creating folder:', error);
      alert('Failed to create folder. Please try again.');
    }
  }, [loadNotesTree]);

  const handleDeleteNode = useCallback(async (nodeId: string) => {
    try {
      await window.electronAPI.deleteNote(nodeId, true); // Delete with children
      await loadNotesTree();
    } catch (error) {
      console.error('Error deleting node:', error);
    }
  }, [loadNotesTree]);

  const handleRenameNode = useCallback(async (nodeId: string, newName: string) => {
    try {
      // Find the node to determine if it's a folder or note
      const findNode = (nodes: NoteNode[]): NoteNode | null => {
        for (const node of nodes) {
          if (node.id === nodeId) return node;
          const found = findNode(node.children);
          if (found) return found;
        }
        return null;
      };

      const node = findNode(notesTree);
      if (node) {
        if (node.isFolder) {
          // For folders, update the title (content is empty for folders)
          await window.electronAPI.updateNote(nodeId, '', newName);
        } else {
          // For notes, update the title but keep content
          await window.electronAPI.updateNote(nodeId, node.content, newName);
        }
        await loadNotesTree();
      }
    } catch (error) {
      console.error('Error renaming node:', error);
    }
  }, [notesTree, loadNotesTree]);

  // Inline editing handlers
  const handleStartEdit = (nodeId: string) => {
    console.log('Starting edit for node:', nodeId);
    console.log('Current editingNodeId before:', editingNodeId);
    setEditingNodeId(nodeId);
    console.log('Edit mode should now be active for:', nodeId);
  };

  const handleFinishEdit = useCallback(async (nodeId: string, newName: string) => {
    try {
      const trimmedName = newName.trim();

      if (!trimmedName) {
        // Empty name - cancel edit
        setEditingNodeId(null);
        return;
      }

      // Basic name validation - prevent problematic characters
      const invalidChars = /[<>:"/\\|?*]/;
      if (invalidChars.test(trimmedName)) {
        alert('Name cannot contain the following characters: < > : " / \\ | ? *');
        return; // Keep editing mode active
      }

      // Prevent names that are too long
      if (trimmedName.length > 255) {
        alert('Name is too long. Please use a shorter name (maximum 255 characters).');
        return; // Keep editing mode active
      }

      // Find the current node and its siblings for duplicate checking
      const findNodeAndSiblings = (nodes: NoteNode[], targetId: string): { node: NoteNode | null, siblings: NoteNode[] } => {
        for (const node of nodes) {
          if (node.id === targetId) {
            return { node, siblings: nodes };
          }
          const result = findNodeAndSiblings(node.children, targetId);
          if (result.node) return result;
        }
        return { node: null, siblings: [] };
      };

      const { node, siblings } = findNodeAndSiblings(notesTree, nodeId);

      if (!node) {
        console.error('Node not found:', nodeId);
        setEditingNodeId(null);
        return;
      }

      // Check for duplicate names among siblings (case-insensitive)
      const duplicateExists = siblings.some(sibling =>
        sibling.id !== nodeId &&
        sibling.title &&
        sibling.title.toLowerCase() === trimmedName.toLowerCase()
      );

      if (duplicateExists) {
        alert(`A ${node.isFolder ? 'folder' : 'note'} with the name "${trimmedName}" already exists in this location.`);
        return; // Keep editing mode active
      }

      // Update the note/folder name and mark as permanent if it was temporary
      if (node.isFolder) {
        await window.electronAPI.updateNote(nodeId, '', trimmedName);
      } else {
        await window.electronAPI.updateNote(nodeId, node.content, trimmedName);
      }

      // If this was a temporary item, we need to mark it as permanent
      if (node.isTemporary) {
        console.log(`Marking temporary item ${nodeId} as permanent after successful edit`);
        // The updateNote call above should handle this, but let's be explicit
        // We'll need to add a specific API call to mark items as permanent
      }

      await loadNotesTree();
      setEditingNodeId(null);

      // If this node was selected, refresh it to show updated content
      if (selectedNodeId === nodeId) {
        // Find the updated node in the fresh tree data and trigger a re-selection
        setTimeout(async () => {
          try {
            const freshTree = await window.electronAPI.getNotesTree();
            const findUpdatedNode = (nodes: NoteNode[]): NoteNode | null => {
              for (const node of nodes) {
                if (node.id === nodeId) return node;
                const found = findUpdatedNode(node.children);
                if (found) return found;
              }
              return null;
            };

            const updatedNode = findUpdatedNode(freshTree);
            if (updatedNode && !updatedNode.isFolder) {
              onNodeSelect(updatedNode);
            }
          } catch (error) {
            console.error('Error refreshing node content:', error);
          }
        }, 100); // Small delay to ensure tree is updated
      }

      console.log('Finished editing node:', nodeId, 'with name:', trimmedName);
    } catch (error) {
      console.error('Error finishing edit:', error);
      setEditingNodeId(null);
    }
  }, [notesTree, loadNotesTree]);

  const handleCancelEdit = useCallback(async (nodeId: string) => {
    try {
      // Check if this was a newly created item by looking for temporary flag
      const findNode = (nodes: NoteNode[]): NoteNode | null => {
        for (const node of nodes) {
          if (node.id === nodeId) return node;
          const found = findNode(node.children);
          if (found) return found;
        }
        return null;
      };

      const node = findNode(notesTree);
      if (node?.isTemporary) {
        // Delete the temporary item
        await window.electronAPI.deleteNote(nodeId, true);
        await loadNotesTree();
        console.log('Cancelled edit and deleted temporary node:', nodeId);
      }

      setEditingNodeId(null);
    } catch (error) {
      console.error('Error cancelling edit:', error);
      setEditingNodeId(null);
    }
  }, [notesTree, loadNotesTree]);

  const handleSearchResultClick = (result: SearchResult) => {
    // Create a temporary NoteNode from SearchResult
    const noteNode: NoteNode = {
      id: result.id,
      title: result.title,
      content: result.content,
      parentId: null,
      children: [],
      isFolder: false,
      isExpanded: false,
      isPinned: false,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    onNodeSelect(noteNode);
    // Clear search to show the tree again
    // This would need to be communicated back to parent component
  };

  // Drag and drop handlers
  const handleDragStart = useCallback((nodeId: string) => {
    console.log('Drag started for node:', nodeId);
  }, []);

  const handleDragOver = useCallback((targetId: string, position: 'above' | 'below' | 'inside') => {
    // Visual feedback is handled by the TreeNode component
  }, []);

  const handleDrop = useCallback(async (draggedId: string, targetId: string, position: 'above' | 'below' | 'inside') => {
    try {
      console.log('Drop:', { draggedId, targetId, position });
      
      // Find the target node to determine the new parent
      const findNode = (nodes: NoteNode[], id: string): NoteNode | null => {
        for (const node of nodes) {
          if (node.id === id) return node;
          if (node.children) {
            const found = findNode(node.children, id);
            if (found) return found;
          }
        }
        return null;
      };

      const targetNode = findNode(notesTree, targetId);
      if (!targetNode) {
        console.error('Target node not found:', targetId);
        return;
      }

      let newParentId: string | null;
      
      if (position === 'inside' && targetNode.isFolder) {
        // Move inside the target folder
        newParentId = targetId;
      } else {
        // Move to the same level as the target (sibling)
        newParentId = targetNode.parentId;
      }

      await moveNote(draggedId, newParentId);
      console.log('Note moved successfully');
      
    } catch (error) {
      console.error('Error dropping note:', error);
    }
  }, [notesTree, moveNote]);

  // If searching, show search results
  if (searchQuery.trim()) {
    return (
      <div className="flex flex-col h-full">
        {/* Search Results Header */}
        <div className="notes-tree-header">
          <div className="flex items-center justify-between">
            <h2 className="notes-tree-title">
              Search Results ({searchResults.length})
            </h2>
            <button
              onClick={() => {
                console.log('Search New Note button clicked');
                handleCreateNote();
              }}
              className="font-medium py-1 px-3 rounded-lg text-sm transition-all duration-200"
              style={{ 
                color: 'var(--text-secondary)',
                backgroundColor: 'transparent' 
              }}
              onMouseEnter={(e) => {
                (e.target as HTMLElement).style.backgroundColor = 'var(--bg-tertiary)';
                (e.target as HTMLElement).style.color = 'var(--text-primary)';
              }}
              onMouseLeave={(e) => {
                (e.target as HTMLElement).style.backgroundColor = 'transparent';
                (e.target as HTMLElement).style.color = 'var(--text-secondary)';
              }}
              title="New Note"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8z"/>
                <polyline points="14,2 14,8 20,8"/>
                <line x1="12" y1="11" x2="12" y2="17"/>
                <line x1="9" y1="14" x2="15" y2="14"/>
              </svg>
            </button>
          </div>
        </div>

        {/* Search Results List */}
        <div className="flex-1 overflow-y-auto custom-scrollbar">
          {isSearching ? (
            <div className="p-4 text-center" style={{ color: 'var(--text-secondary)' }}>
              Searching...
            </div>
          ) : searchResults.length === 0 ? (
            <div className="p-4 text-center" style={{ color: 'var(--text-secondary)' }}>
              No results found for &quot;{searchQuery}&quot;
            </div>
          ) : (
            searchResults.map((result) => (
              <div
                key={result.id}
                onClick={() => handleSearchResultClick(result)}
                className="p-4 cursor-pointer transition-colors"
                style={{
                  borderBottom: '1px solid var(--border-color)'
                }}
                onMouseEnter={(e) => {
                  (e.target as HTMLElement).style.backgroundColor = 'var(--bg-secondary)';
                }}
                onMouseLeave={(e) => {
                  (e.target as HTMLElement).style.backgroundColor = 'transparent';
                }}
              >
                <div className="font-medium text-truncate" style={{ color: 'var(--text-primary)' }}>
                  {result.title}
                </div>
                {result.snippet && (
                  <div
                    className="text-sm mt-1"
                    style={{ color: 'var(--text-secondary)' }}
                    dangerouslySetInnerHTML={{ __html: result.snippet }}
                  />
                )}
                {result.path.length > 0 && (
                  <div className="text-xs mt-2 flex items-center" style={{ color: 'var(--text-tertiary)' }}>
                    <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M2 6a2 2 0 012-2h5l2 2h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" />
                    </svg>
                    {result.path.join(' > ')}
                  </div>
                )}
              </div>
            ))
          )}
        </div>
      </div>
    );
  }

  // Normal tree view - debug logs for folder creation
  console.log('TreeSidebar render:', {
    searchQuery,
    tree: notesTree.length,
    searchResults: searchResults.length,
    isLoadingTree,
    isSearching,
    editingNodeId,
    newlyCreatedNodeId
  });

  console.log('TreeSidebar: notesTree type:', typeof notesTree);
  console.log('TreeSidebar: notesTree is array:', Array.isArray(notesTree));
  console.log('TreeSidebar: notesTree value:', notesTree);

  if (notesTree.length > 0) {
    console.log('Tree nodes to render:', notesTree.map(n => ({ id: n.id, title: n.title, isFolder: n.isFolder })));
  } else {
    console.log('TreeSidebar: No tree nodes to render - tree is empty or invalid');
  }

  return (
    <div className="w-full flex flex-col h-full" style={{
      backgroundColor: 'var(--bg-primary)'
    }}>
      {/* Tree Header */}
      <div className="notes-tree-header">
        <div className="flex items-center justify-between w-full">
          <h2 className="notes-tree-title">Notes (Tree)</h2>
          <div className="flex items-center space-x-1">
            <button
              onClick={() => {
                console.log('New Folder button clicked, selectedNodeId:', selectedNodeId);
                // If a folder is selected, create under it; otherwise create at root
                const selectedNode = selectedNodeId ? findNodeInTree(selectedNodeId) : null;
                const parentId = selectedNode?.isFolder ? selectedNodeId : undefined;
                handleCreateFolder(parentId);
              }}
              className="font-medium py-1 px-3 rounded-lg text-sm transition-all duration-200"
              style={{ 
                color: 'var(--text-secondary)',
                backgroundColor: 'transparent' 
              }}
              onMouseEnter={(e) => {
                (e.target as HTMLElement).style.backgroundColor = 'var(--bg-tertiary)';
                (e.target as HTMLElement).style.color = 'var(--text-primary)';
              }}
              onMouseLeave={(e) => {
                (e.target as HTMLElement).style.backgroundColor = 'transparent';
                (e.target as HTMLElement).style.color = 'var(--text-secondary)';
              }}
              title={selectedNodeId ? "New Folder under selected" : "New Folder"}
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M22 19a2 2 0 01-2 2H4a2 2 0 01-2-2V5a2 2 0 012-2h5l2 3h9a2 2 0 012 2z"/>
                <line x1="12" y1="11" x2="12" y2="17"/>
                <line x1="9" y1="14" x2="15" y2="14"/>
              </svg>
            </button>
            <button
              onClick={() => {
                console.log('New Note button clicked, selectedNodeId:', selectedNodeId);
                // If a folder is selected, create under it; otherwise create at root
                const selectedNode = selectedNodeId ? findNodeInTree(selectedNodeId) : null;
                const parentId = selectedNode?.isFolder ? selectedNodeId : undefined;
                handleCreateNote(parentId);
              }}
              className="font-medium py-1 px-3 rounded-lg text-sm transition-all duration-200"
              style={{ 
                color: 'var(--text-secondary)',
                backgroundColor: 'transparent' 
              }}
              onMouseEnter={(e) => {
                (e.target as HTMLElement).style.backgroundColor = 'var(--bg-tertiary)';
                (e.target as HTMLElement).style.color = 'var(--text-primary)';
              }}
              onMouseLeave={(e) => {
                (e.target as HTMLElement).style.backgroundColor = 'transparent';
                (e.target as HTMLElement).style.color = 'var(--text-secondary)';
              }}
              title={selectedNodeId ? "New Note under selected" : "New Note"}
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8z"/>
                <polyline points="14,2 14,8 20,8"/>
                <line x1="12" y1="11" x2="12" y2="17"/>
                <line x1="9" y1="14" x2="15" y2="14"/>
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Tree View */}
      <div className="flex-1 overflow-y-auto custom-scrollbar">
        {isLoadingTree ? (
          <div className="p-4 text-center text-gray-500">
            Loading notes...
          </div>
        ) : notesTree.length === 0 ? (
          <div className="p-4 text-center text-gray-500">
            No notes yet. Create your first note!
          </div>
        ) : (
          <div className="py-2">
            {notesTree.map((node) => (
              <TreeNode
                key={node.id}
                node={node}
                level={0}
                selectedNodeId={selectedNodeId}
                editingNodeId={editingNodeId || undefined}
                newlyCreatedNodeId={newlyCreatedNodeId || undefined}
                onNodeSelect={onNodeSelect}
                onToggleFolder={handleToggleFolder}
                onCreateNote={handleCreateNote}
                onCreateFolder={handleCreateFolder}
                onDeleteNode={handleDeleteNode}
                onRenameNode={handleRenameNode}
                onStartEdit={handleStartEdit}
                onFinishEdit={handleFinishEdit}
                onCancelEdit={handleCancelEdit}
                onDragStart={handleDragStart}
                onDragOver={handleDragOver}
                onDrop={handleDrop}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
}