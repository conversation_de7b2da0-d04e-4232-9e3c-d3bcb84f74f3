import React, { useState, useEffect } from 'react';

interface SaveStatusIndicatorProps {
  status: 'idle' | 'saving' | 'saved' | 'error';
  lastSaved: Date | null;
  onRetry?: () => void;
}

export function SaveStatusIndicator({ status, lastSaved, onRetry }: SaveStatusIndicatorProps) {
  const [showSuccess, setShowSuccess] = useState(false);
  
  useEffect(() => {
    if (status === 'saved' && lastSaved) {
      setShowSuccess(true);
      const timer = setTimeout(() => setShowSuccess(false), 1000);
      return () => clearTimeout(timer);
    }
  }, [status, lastSaved]);
  
  const formatRelativeTime = (date: Date) => {
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diffInSeconds < 60) {
      return 'just now';
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes}m ago`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `${hours}h ago`;
    } else {
      const days = Math.floor(diffInSeconds / 86400);
      return `${days}d ago`;
    }
  };

  return (
    <div className="save-indicator relative flex items-center h-6">
      <div 
        className={`save-icon text-base transition-colors duration-300 ${
          status === 'saving' ? 'save-icon-saving' : 'save-icon-idle'
        }`}
        style={{
          color: status === 'saving' ? 'var(--text-primary)' : 'var(--text-tertiary)'
        }}
      >
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M19 21H5a2 2 0 01-2-2V5a2 2 0 012-2h11l5 5v11a2 2 0 01-2 2z"/>
          <polyline points="17,21 17,13 7,13 7,21"/>
          <polyline points="7,3 7,8 15,8"/>
        </svg>
      </div>
      {showSuccess && (
        <div 
          className="save-checkmark absolute -top-0.5 -right-0.5 text-xs animate-fade-in-out"
          style={{ color: 'var(--accent-color)' }}
        >
          <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="3">
            <polyline points="20,6 9,17 4,12"/>
          </svg>
        </div>
      )}
      {status === 'error' && (
        <button
          onClick={onRetry}
          className="ml-1 text-red-600 text-xs hover:text-red-700 cursor-pointer"
          title="Click to retry saving"
        >
          ⚠️
        </button>
      )}
    </div>
  );
}