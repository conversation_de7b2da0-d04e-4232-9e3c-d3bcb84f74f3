import React, { useState, useEffect, useCallback } from 'react';

interface ToggleShortcutEditorProps {
  isOpen: boolean;
  onClose: () => void;
}

export function ToggleShortcutEditor({ isOpen, onClose }: ToggleShortcutEditorProps) {
  const [currentShortcut, setCurrentShortcut] = useState('CommandOrControl+Shift+Space');
  const [isRecording, setIsRecording] = useState(false);
  const [tempShortcut, setTempShortcut] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  

  // Load current toggle shortcut
  useEffect(() => {
    if (isOpen) {
      loadCurrentShortcut();
    }
  }, [isOpen]);

  const loadCurrentShortcut = async () => {
    try {
      setIsLoading(true);
      const settings = await window.electronAPI.getShortcutSettings();
      const toggleShortcut = settings.shortcuts['toggle-window'] || 'CommandOrControl+Shift+Space';
      
      setCurrentShortcut(toggleShortcut);
      setTempShortcut(toggleShortcut);
    } catch (error) {
      console.error('Error loading shortcut:', error);
      setError('Failed to load current shortcut');
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (!isRecording) return;

    event.preventDefault();
    event.stopPropagation();

    // Build shortcut string
    const parts: string[] = [];
    
    if (event.metaKey || event.ctrlKey) {
      parts.push('CommandOrControl');
    }
    if (event.altKey) {
      parts.push('Alt');
    }
    if (event.shiftKey) {
      parts.push('Shift');
    }
    
    // Add the key
    let key = event.key;
    if (key === ' ') {
      key = 'Space';
    } else if (key.length === 1) {
      key = key.toUpperCase();
    } else if (key.startsWith('F') && /F\d+/.test(key)) {
      key = key; // F1, F2, etc.
    } else {
      // Special keys
      const keyMap: Record<string, string> = {
        'Enter': 'Enter',
        'Escape': 'Escape',
        'Tab': 'Tab',
        'Backspace': 'Backspace',
        'Delete': 'Delete',
        'Insert': 'Insert',
        'Home': 'Home',
        'End': 'End',
        'PageUp': 'PageUp',
        'PageDown': 'PageDown',
        'ArrowUp': 'Up',
        'ArrowDown': 'Down',
        'ArrowLeft': 'Left',
        'ArrowRight': 'Right'
      };
      key = keyMap[key] || key;
    }

    if (key && key !== 'Meta' && key !== 'Control' && key !== 'Alt' && key !== 'Shift') {
      parts.push(key);
    }

    if (parts.length > 1) {
      const shortcut = parts.join('+');
      if (isRecording) {
        setTempShortcut(shortcut);
      }
    }
  }, [isRecording]);

  useEffect(() => {
    if (isRecording) {
      document.addEventListener('keydown', handleKeyDown, true);
      return () => {
        document.removeEventListener('keydown', handleKeyDown, true);
      };
    }
  }, [isRecording, handleKeyDown]);

  const startRecording = () => {
    setIsRecording(true);
    setTempShortcut('');
    setError('');
  };

  const stopRecording = () => {
    setIsRecording(false);
  };

  const saveShortcut = async () => {
    if (!tempShortcut) {
      setError('Please enter a valid shortcut');
      return;
    }

    try {
      // Test if shortcut is available
      const isToggleAvailable = await window.electronAPI.testShortcut(tempShortcut, 'toggle-window');
      
      if (!isToggleAvailable) {
        setError('Toggle window shortcut is already in use by the system or another application');
        return;
      }

      // Update settings
      const newSettings = {
        shortcuts: {
          'toggle-window': tempShortcut
        }
      };

      await window.electronAPI.updateShortcutSettings(newSettings);
      setCurrentShortcut(tempShortcut);
      setError('');
      onClose();
    } catch (error) {
      console.error('Error saving shortcut:', error);
      setError('Failed to save shortcut');
    }
  };

  const cancelEdit = () => {
    setTempShortcut(currentShortcut);
    setIsRecording(false);
    setError('');
  };

  const resetToDefault = () => {
    const defaultShortcut = 'CommandOrControl+Shift+Space';
    setTempShortcut(defaultShortcut);
    setError('');
  };


  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md overflow-hidden">
        <div className="flex items-center justify-between p-4 border-b dark:border-gray-700">
          <h2 className="text-lg font-semibold dark:text-white">Keyboard Shortcuts</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            ✕
          </button>
        </div>

        <div className="p-4">
          {error && (
            <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded text-sm">
              {error}
            </div>
          )}

          {isLoading ? (
            <div className="text-center py-4">
              <div className="text-gray-500 dark:text-gray-400">Loading...</div>
            </div>
          ) : (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Show/Hide Window Shortcut
                </label>
                <div className="flex items-center space-x-3">
                  <kbd className={`px-3 py-2 text-sm font-mono rounded border flex-1 text-center ${
                    isRecording
                      ? 'bg-blue-100 border-blue-300 text-blue-800 dark:bg-blue-900 dark:border-blue-600 dark:text-blue-200'
                      : 'bg-gray-100 border-gray-300 text-gray-800 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200'
                  }`}>
                    {isRecording
                      ? (tempShortcut || 'Press keys...')
                      : tempShortcut
                    }
                  </kbd>
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  This shortcut toggles the application window visibility
                </p>
              </div>

              <div className="flex items-center space-x-2">
                {isRecording ? (
                  <>
                    <button
                      onClick={stopRecording}
                      className="px-3 py-2 text-sm bg-blue-600 text-white rounded hover:bg-blue-700"
                    >
                      Stop Recording
                    </button>
                  </>
                ) : (
                  <>
                    <button
                      onClick={startRecording}
                      className="px-3 py-2 text-sm bg-blue-600 text-white rounded hover:bg-blue-700"
                    >
                      Change Shortcut
                    </button>
                    <button
                      onClick={resetToDefault}
                      className="px-3 py-2 text-sm bg-gray-600 text-white rounded hover:bg-gray-700"
                    >
                      Reset
                    </button>
                  </>
                )}
              </div>

            </div>
          )}
        </div>

        <div className="flex items-center justify-end space-x-3 p-4 border-t dark:border-gray-700">
          <button
            onClick={cancelEdit}
            className="px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
          >
            Cancel
          </button>
          <button
            onClick={saveShortcut}
            className="px-4 py-2 text-sm bg-blue-600 text-white rounded hover:bg-blue-700"
            disabled={isRecording || !tempShortcut}
          >
            Save
          </button>
        </div>
      </div>
    </div>
  );
}