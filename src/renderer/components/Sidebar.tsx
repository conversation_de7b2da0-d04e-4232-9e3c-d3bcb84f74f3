type ViewType = 'notes' | 'translator' | 'calculator' | 'clipboard' | 'converter' | 'trash';

interface SidebarProps {
  currentView: ViewType;
  onViewChange: (view: ViewType) => void;
  onOpenSearch?: () => void;
  theme?: 'light' | 'dark';
  onThemeToggle?: () => void;
}

import React, { useState } from 'react';
import { ToggleShortcutEditor } from './ToggleShortcutEditor';

export function Sidebar({ currentView, onViewChange, onOpenSearch, theme, onThemeToggle }: SidebarProps) {
  const [showToggleShortcutEditor, setShowToggleShortcutEditor] = useState(false);
  // Removed collapse functionality - sidebar is always expanded

  const menuItems = [
    {
      id: 'notes' as ViewType,
      label: 'Notes',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      ),
      shortcut: ''
    },
    {
      id: 'translator' as ViewType,
      label: 'Translator',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
        </svg>
      ),
      shortcut: ''
    },
    {
      id: 'calculator' as ViewType,
      label: 'Calculator',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
        </svg>
      ),
      shortcut: ''
    },
    {
      id: 'converter' as ViewType,
      label: 'Converter',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
        </svg>
      ),
      shortcut: ''
    },
    {
      id: 'clipboard' as ViewType,
      label: 'Clipboard',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
        </svg>
      ),
      shortcut: ''
    },
    {
      id: 'trash' as ViewType,
      label: 'Trash',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
        </svg>
      ),
      shortcut: ''
    }
  ];

  return (
    <div className="h-full flex flex-col w-full">
      {/* Sidebar Header */}
      <div className="sidebar-header">
        <h1 className="sidebar-title">Nexus</h1>
        <div className="sidebar-controls" style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          {onOpenSearch && (
            <button
              className="search-trigger-btn"
              onClick={onOpenSearch}
              title="Search notes (⌘K)"
              style={{
                background: 'none',
                border: 'none',
                fontSize: '14px',
                cursor: 'pointer',
                padding: '4px',
                borderRadius: '4px',
                color: 'var(--text-secondary)'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
                e.currentTarget.style.color = 'var(--text-primary)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent';
                e.currentTarget.style.color = 'var(--text-secondary)';
              }}
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <circle cx="11" cy="11" r="8"/>
                <path d="m21 21-4.35-4.35"/>
              </svg>
            </button>
          )}
          {theme && onThemeToggle && (
            <button
              onClick={onThemeToggle}
              title={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}
              style={{
                background: 'none',
                border: 'none',
                fontSize: '14px',
                cursor: 'pointer',
                padding: '4px',
                borderRadius: '4px',
                color: 'var(--text-secondary)'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
                e.currentTarget.style.color = 'var(--text-primary)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent';
                e.currentTarget.style.color = 'var(--text-secondary)';
              }}
            >
              {theme === 'dark' ? (
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <circle cx="12" cy="12" r="5"/>
                  <path d="M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42"/>
                </svg>
              ) : (
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"/>
                </svg>
              )}
            </button>
          )}
        </div>
      </div>

      {/* Navigation */}
      <div className="flex-1 overflow-y-auto">
        <nav className="p-3 space-y-1">
          {menuItems.map((item) => (
            <button
              key={item.id}
              onClick={() => onViewChange(item.id)}
              className="w-full flex items-center justify-between px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-150"
              style={{
                color: currentView === item.id ? 'var(--accent-color)' : 'var(--text-secondary)',
                backgroundColor: currentView === item.id ? 'var(--bg-tertiary)' : 'transparent'
              }}
              onMouseEnter={(e) => {
                if (currentView !== item.id) {
                  const button = e.currentTarget;
                  button.style.backgroundColor = 'var(--bg-tertiary)';
                  button.style.color = 'var(--text-primary)';
                }
              }}
              onMouseLeave={(e) => {
                if (currentView !== item.id) {
                  const button = e.currentTarget;
                  button.style.backgroundColor = 'transparent';
                  button.style.color = 'var(--text-secondary)';
                }
              }}
            >
              <span className="flex items-center">
                {item.icon}
                <span className="ml-3">{item.label}</span>
              </span>
              <span className="text-xs" style={{ color: 'var(--text-tertiary)' }}>{item.shortcut}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Footer */}
      <div className="p-3 border-t" style={{ 
        borderColor: 'var(--border-color)', 
        backgroundColor: 'var(--bg-secondary)' 
      }}>
        <div className="flex flex-col gap-2">
          {/* Settings buttons */}
          <div className="flex items-center justify-start gap-2">
            <button
              onClick={() => setShowToggleShortcutEditor(true)}
              className="text-xs transition-colors p-1 rounded"
              style={{ color: 'var(--text-secondary)' }}
              onMouseEnter={(e) => {
                e.currentTarget.style.color = 'var(--text-primary)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.color = 'var(--text-secondary)';
              }}
              title="Edit Keyboard Shortcuts"
            >
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" style={{ marginRight: '6px', display: 'inline' }}>
                <rect x="2" y="6" width="20" height="12" rx="2"/>
                <circle cx="6" cy="10" r="1"/>
                <circle cx="10" cy="10" r="1"/>
                <circle cx="14" cy="10" r="1"/>
                <circle cx="18" cy="10" r="1"/>
                <rect x="6" y="14" width="8" height="1" rx="0.5"/>
              </svg>
              Shortcuts
            </button>
          </div>
          
          {/* Footer info */}
          <div className="space-y-1">
            <div className="flex items-center justify-start text-xs" style={{ color: 'var(--text-tertiary)' }}>
              <span>⌘⇧Space to toggle</span>
            </div>
          </div>
        </div>
      </div>
      
      {/* Toggle Shortcut Editor */}
      <ToggleShortcutEditor 
        isOpen={showToggleShortcutEditor}
        onClose={() => setShowToggleShortcutEditor(false)}
      />
    </div>
  );
}
