import React, { useState, useEffect } from 'react';

interface ConversionUnit {
  id: string;
  name: string;
  symbol: string;
  category: 'weight' | 'length' | 'temperature';
}

const units: ConversionUnit[] = [
  // Weight units
  { id: 'lbs', name: 'Pounds', symbol: 'lbs', category: 'weight' },
  { id: 'kg', name: 'Kilograms', symbol: 'kg', category: 'weight' },
  { id: 'oz', name: 'Ounces', symbol: 'oz', category: 'weight' },
  { id: 'g', name: 'Grams', symbol: 'g', category: 'weight' },
  
  // Length units
  { id: 'ft', name: 'Feet', symbol: 'ft', category: 'length' },
  { id: 'm', name: 'Meters', symbol: 'm', category: 'length' },
  { id: 'in', name: 'Inches', symbol: 'in', category: 'length' },
  { id: 'cm', name: 'Centimeters', symbol: 'cm', category: 'length' },
  { id: 'mi', name: 'Miles', symbol: 'mi', category: 'length' },
  { id: 'km', name: 'Kilometers', symbol: 'km', category: 'length' },
  
  // Temperature units
  { id: 'f', name: 'Fahrenheit', symbol: '°F', category: 'temperature' },
  { id: 'c', name: 'Celsius', symbol: '°C', category: 'temperature' },
  { id: 'k', name: 'Kelvin', symbol: 'K', category: 'temperature' },
];

// Base conversion factors to a common unit (grams for weight, meters for length)
const baseConversions = {
  // Weight to grams
  weight: {
    lbs: 453.592,
    kg: 1000,
    oz: 28.3495,
    g: 1,
  },
  // Length to meters
  length: {
    ft: 0.3048,
    m: 1,
    in: 0.0254,
    cm: 0.01,
    mi: 1609.34,
    km: 1000,
  },
  // Temperature (handled separately due to offset formulas)
  temperature: {}
};

// Function to convert between any two units
const convertUnits = (value: number, fromUnit: string, toUnit: string, category: string): number => {
  if (fromUnit === toUnit) return value;
  
  if (category === 'temperature') {
    // Handle temperature conversions with special formulas
    if (fromUnit === 'f' && toUnit === 'c') return (value - 32) * 5/9;
    if (fromUnit === 'c' && toUnit === 'f') return (value * 9/5) + 32;
    if (fromUnit === 'c' && toUnit === 'k') return value + 273.15;
    if (fromUnit === 'k' && toUnit === 'c') return value - 273.15;
    if (fromUnit === 'f' && toUnit === 'k') return (value - 32) * 5/9 + 273.15;
    if (fromUnit === 'k' && toUnit === 'f') return (value - 273.15) * 9/5 + 32;
    return value;
  }
  
  // For weight and length, convert through base unit
  const conversions = baseConversions[category as keyof typeof baseConversions] as Record<string, number>;
  if (!conversions || !conversions[fromUnit] || !conversions[toUnit]) {
    return value;
  }
  
  // Convert from source unit to base unit, then to target unit
  const baseValue = value * conversions[fromUnit];
  return baseValue / conversions[toUnit];
};

export function UnitConverter() {
  // Use localStorage to persist selections
  const [value, setValue] = useState('');
  const [fromUnit, setFromUnit] = useState(() => {
    return localStorage.getItem('converter-from-unit') || 'lbs';
  });
  const [toUnit, setToUnit] = useState(() => {
    return localStorage.getItem('converter-to-unit') || 'kg';
  });
  const [result, setResult] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<'weight' | 'length' | 'temperature'>(() => {
    return (localStorage.getItem('converter-category') as 'weight' | 'length' | 'temperature') || 'weight';
  });

  // Filter units by category
  const availableUnits = units.filter(unit => unit.category === selectedCategory);

  // Persist selections to localStorage
  useEffect(() => {
    localStorage.setItem('converter-from-unit', fromUnit);
  }, [fromUnit]);

  useEffect(() => {
    localStorage.setItem('converter-to-unit', toUnit);
  }, [toUnit]);

  useEffect(() => {
    localStorage.setItem('converter-category', selectedCategory);
  }, [selectedCategory]);

  // Handle category change and update units accordingly
  const handleCategoryChange = (newCategory: 'weight' | 'length' | 'temperature') => {
    setSelectedCategory(newCategory);
    const options = units.filter(unit => unit.category === newCategory);
    
    // Reset to first two options of new category if current units don't exist
    if (!options.find(opt => opt.id === fromUnit)) {
      setFromUnit(options[0]?.id || 'lbs');
    }
    if (!options.find(opt => opt.id === toUnit)) {
      setToUnit(options[1]?.id || 'kg');
    }
  };

  // Debug console logging
  useEffect(() => {
    console.log('UnitConverter state:', {
      selectedCategory,
      fromUnit,
      toUnit,
      value,
      result
    });
  }, [selectedCategory, fromUnit, toUnit, value, result]);

  // Perform conversion
  useEffect(() => {
    if (value && !isNaN(Number(value))) {
      const numValue = Number(value);
      const converted = convertUnits(numValue, fromUnit, toUnit, selectedCategory);
      setResult(converted.toFixed(6).replace(/\.?0+$/, '')); // Remove trailing zeros
    } else {
      setResult('');
    }
  }, [value, fromUnit, toUnit, selectedCategory]);

  const getUnitSymbol = (unitId: string): string => {
    const unit = units.find(u => u.id === unitId);
    return unit?.symbol || unitId;
  };

  return (
    <div className="h-full flex flex-col" style={{ backgroundColor: 'var(--bg-primary)' }}>
      <div className="flex-1 p-6">
        <div className="max-w-md mx-auto">
          <h2 className="text-2xl font-semibold mb-6" style={{ color: 'var(--text-primary)' }}>Unit Converter</h2>
          
          {/* Category Selection */}
          <div className="mb-6">
            <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>Category</label>
            <div className="flex space-x-2">
              {(['weight', 'length', 'temperature'] as const).map((category) => (
                <button
                  key={category}
                  onClick={() => handleCategoryChange(category)}
                  className="px-3 py-1 text-sm rounded-lg transition-colors"
                  style={{
                    backgroundColor: selectedCategory === category ? 'var(--accent-color)' : 'var(--bg-secondary)',
                    color: selectedCategory === category ? 'white' : 'var(--text-primary)'
                  }}
                  onMouseEnter={(e) => {
                    if (selectedCategory !== category) {
                      (e.target as HTMLElement).style.backgroundColor = 'var(--bg-tertiary)';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (selectedCategory !== category) {
                      (e.target as HTMLElement).style.backgroundColor = 'var(--bg-secondary)';
                    }
                  }}
                >
                  {category.charAt(0).toUpperCase() + category.slice(1)}
                </button>
              ))}
            </div>
          </div>

          {/* Input Value */}
          <div className="mb-6">
            <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>Value</label>
            <input
              type="number"
              value={value}
              onChange={(e) => setValue(e.target.value)}
              placeholder="Enter value..."
              className="w-full px-3 py-2 text-lg rounded-md border transition-colors"
              style={{
                backgroundColor: 'var(--bg-primary)',
                color: 'var(--text-primary)',
                borderColor: 'var(--border-color)'
              }}
              onFocus={(e) => {
                (e.target as HTMLElement).style.borderColor = 'var(--accent-color)';
              }}
              onBlur={(e) => {
                (e.target as HTMLElement).style.borderColor = 'var(--border-color)';
              }}
              step="any"
            />
          </div>

          {/* Unit Selectors */}
          <div className="mb-6">
            <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>Convert From/To</label>
            <div className="flex items-center space-x-3">
              <select
                value={fromUnit}
                onChange={(e) => {
                  console.log('From unit changing from', fromUnit, 'to', e.target.value);
                  setFromUnit(e.target.value);
                }}
                className="flex-1 px-3 py-2 rounded-md border transition-colors unit-select"
                style={{
                  appearance: 'auto',
                  backgroundColor: 'var(--bg-primary)',
                  color: 'var(--text-primary)',
                  borderColor: 'var(--border-color)',
                  cursor: 'pointer'
                }}
              >
                {availableUnits.map((unit) => (
                  <option key={unit.id} value={unit.id}>
                    {unit.name} ({unit.symbol})
                  </option>
                ))}
              </select>
              
              <button 
                className="swap-units-btn px-2 py-2 text-xl rounded-md transition-colors"
                onClick={() => {
                  const temp = fromUnit;
                  setFromUnit(toUnit);
                  setToUnit(temp);
                }}
                title="Swap units"
                style={{
                  background: 'none',
                  border: '1px solid var(--border-color)',
                  cursor: 'pointer',
                  color: 'var(--text-secondary)'
                }}
                onMouseEnter={(e) => {
                  (e.target as HTMLElement).style.color = 'var(--accent-color)';
                  (e.target as HTMLElement).style.borderColor = 'var(--accent-color)';
                }}
                onMouseLeave={(e) => {
                  (e.target as HTMLElement).style.color = 'var(--text-secondary)';
                  (e.target as HTMLElement).style.borderColor = 'var(--border-color)';
                }}
              >
                ⇄
              </button>
              
              <select
                value={toUnit}
                onChange={(e) => {
                  console.log('To unit changing from', toUnit, 'to', e.target.value);
                  setToUnit(e.target.value);
                }}
                className="flex-1 px-3 py-2 rounded-md border transition-colors unit-select"
                style={{
                  appearance: 'auto',
                  backgroundColor: 'var(--bg-primary)',
                  color: 'var(--text-primary)',
                  borderColor: 'var(--border-color)',
                  cursor: 'pointer'
                }}
              >
                {availableUnits.map((unit) => (
                  <option key={unit.id} value={unit.id}>
                    {unit.name} ({unit.symbol})
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Result */}
          {result && (
            <div className="p-4 border rounded-lg" style={{ backgroundColor: 'var(--bg-secondary)', borderColor: 'var(--border-color)' }}>
              <div className="text-sm font-medium" style={{ color: 'var(--accent-color)' }}>Result:</div>
              <div className="text-2xl font-mono" style={{ color: 'var(--text-primary)' }}>
                {result} {getUnitSymbol(toUnit)}
              </div>
            </div>
          )}

          {/* Quick conversions */}
          <div className="mt-6 p-4 rounded-lg" style={{ backgroundColor: 'var(--bg-secondary)' }}>
            <h3 className="text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>Common Conversions:</h3>
            <div className="text-xs space-y-1" style={{ color: 'var(--text-secondary)' }}>
              {selectedCategory === 'weight' && (
                <>
                  <div>• 1 lb = 0.454 kg</div>
                  <div>• 1 kg = 2.205 lbs</div>
                  <div>• 1 lb = 16 oz</div>
                </>
              )}
              {selectedCategory === 'length' && (
                <>
                  <div>• 1 ft = 0.305 m</div>
                  <div>• 1 m = 3.281 ft</div>
                  <div>• 1 in = 2.54 cm</div>
                  <div>• 1 mi = 1.609 km</div>
                </>
              )}
              {selectedCategory === 'temperature' && (
                <>
                  <div>• °F to °C: (°F - 32) × 5/9</div>
                  <div>• °C to °F: (°C × 9/5) + 32</div>
                  <div>• °C to K: °C + 273.15</div>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}