import React, { useState, useEffect, useRef } from 'react';
import { Note } from '../../shared/types';

interface SearchSpotlightProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectNote: (note: Note) => void;
}

interface SearchResult {
  id: string;
  title: string;
  content: string;
  created_at: string;
  updated_at: string;
  parent_id: string | null;
  is_folder: boolean;
  is_expanded?: boolean;
  is_pinned?: boolean;
  snippet?: string;
}

export function SearchSpotlight({ isOpen, onClose, onSelectNote }: SearchSpotlightProps) {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (isOpen) {
      // Focus search input when opened
      setTimeout(() => {
        searchInputRef.current?.focus();
      }, 100);
      
      // Reset state when opened
      setQuery('');
      setResults([]);
      setSelectedIndex(0);
    }
  }, [isOpen]);

  useEffect(() => {
    if (!isOpen) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      } else if (e.key === 'ArrowDown') {
        e.preventDefault();
        setSelectedIndex(prev => Math.min(prev + 1, results.length - 1));
      } else if (e.key === 'ArrowUp') {
        e.preventDefault();
        setSelectedIndex(prev => Math.max(prev - 1, 0));
      } else if (e.key === 'Enter' && results.length > 0) {
        e.preventDefault();
        const selectedResult = results[selectedIndex];
        if (selectedResult) {
          const note: Note = {
            id: selectedResult.id,
            title: selectedResult.title,
            content: selectedResult.content,
            created_at: selectedResult.created_at,
            updated_at: selectedResult.updated_at,
            parent_id: selectedResult.parent_id,
            is_folder: selectedResult.is_folder,
            is_expanded: selectedResult.is_expanded,
            is_pinned: selectedResult.is_pinned
          };
          onSelectNote(note);
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, onClose, results, selectedIndex, onSelectNote]);

  // Debounced search
  useEffect(() => {
    if (!query.trim()) {
      setResults([]);
      setSelectedIndex(0);
      return;
    }

    setIsLoading(true);
    const timer = setTimeout(async () => {
      try {
        if (window.electronAPI) {
          const searchResults = await window.electronAPI.searchNotes(query);
          setResults(searchResults.map((result: any) => ({
            ...result,
            snippet: result.content ? result.content.slice(0, 100) + '...' : ''
          })));
          setSelectedIndex(0);
        }
      } catch (error) {
        console.error('Search error:', error);
        setResults([]);
      } finally {
        setIsLoading(false);
      }
    }, 150);

    return () => clearTimeout(timer);
  }, [query]);

  if (!isOpen) return null;

  return (
    <div 
      className="search-spotlight-overlay"
      onClick={onClose}
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: 'rgba(0, 0, 0, 0.3)',
        backdropFilter: 'blur(4px)',
        zIndex: 1000,
        display: 'flex',
        alignItems: 'flex-start',
        justifyContent: 'center',
        paddingTop: '100px'
      }}
    >
      <div 
        className="search-spotlight"
        onClick={(e) => e.stopPropagation()}
        style={{
          width: '600px',
          maxWidth: '90vw',
          background: 'var(--bg-primary)',
          borderRadius: '12px',
          boxShadow: '0 20px 40px rgba(0, 0, 0, 0.3)',
          overflow: 'hidden',
          border: '1px solid var(--border-color)'
        }}
      >
        <div className="search-input-container">
          <input
            ref={searchInputRef}
            type="text"
            placeholder="Search notes..."
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            style={{
              width: '100%',
              border: 'none',
              outline: 'none',
              padding: '16px 20px',
              fontSize: '18px',
              background: 'transparent',
              color: 'var(--text-primary)'
            }}
          />
        </div>
        
        {isLoading && (
          <div style={{ padding: '20px', textAlign: 'center', color: 'var(--text-secondary)' }}>
            Searching...
          </div>
        )}
        
        {!isLoading && results.length > 0 && (
          <div 
            className="search-results"
            style={{
              maxHeight: '400px',
              overflowY: 'auto',
              borderTop: '1px solid var(--border-color)'
            }}
          >
            {results.map((result, index) => (
              <div
                key={result.id}
                className="search-result-item"
                onClick={() => {
                  const note: Note = {
                    id: result.id,
                    title: result.title,
                    content: result.content,
                    created_at: result.created_at,
                    updated_at: result.updated_at,
                    parent_id: result.parent_id,
                    is_folder: result.is_folder,
                    is_expanded: result.is_expanded,
                    is_pinned: result.is_pinned
                  };
                  onSelectNote(note);
                }}
                style={{
                  padding: '12px 20px',
                  cursor: 'pointer',
                  borderBottom: index < results.length - 1 ? '1px solid var(--border-color)' : 'none',
                  background: index === selectedIndex ? 'var(--bg-tertiary)' : 'transparent',
                  transition: 'background-color 0.1s ease'
                }}
                onMouseEnter={() => setSelectedIndex(index)}
              >
                <div style={{ 
                  fontWeight: '600', 
                  color: 'var(--text-primary)',
                  marginBottom: '4px',
                  fontSize: '14px'
                }}>
                  {result.is_folder ? (
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" style={{ display: 'inline', marginRight: '4px' }}>
                      <path d="M22 19a2 2 0 01-2 2H4a2 2 0 01-2-2V5a2 2 0 012-2h5l2 3h9a2 2 0 012 2z"/>
                    </svg>
                  ) : (
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" style={{ display: 'inline', marginRight: '4px' }}>
                      <path d="M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8z"/>
                      <polyline points="14,2 14,8 20,8"/>
                      <line x1="16" y1="13" x2="8" y2="13"/>
                      <line x1="16" y1="17" x2="8" y2="17"/>
                      <polyline points="10,9 9,9 8,9"/>
                    </svg>
                  )}{result.title || ''}
                </div>
                {result.snippet && (
                  <div style={{ 
                    color: 'var(--text-secondary)', 
                    fontSize: '12px',
                    lineHeight: '1.4'
                  }}>
                    {result.snippet}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
        
        {!isLoading && query && results.length === 0 && (
          <div style={{ 
            padding: '40px 20px', 
            textAlign: 'center', 
            color: 'var(--text-secondary)' 
          }}>
            No notes found for &quot;{query}&quot;
          </div>
        )}
        
        <div 
          className="search-footer"
          style={{
            padding: '8px 16px',
            background: 'var(--bg-secondary)',
            borderTop: '1px solid var(--border-color)'
          }}
        >
          <span style={{ 
            fontSize: '12px', 
            color: 'var(--text-secondary)' 
          }}>
            ↑↓ to navigate • ↵ to select • esc to close
          </span>
        </div>
      </div>
    </div>
  );
}