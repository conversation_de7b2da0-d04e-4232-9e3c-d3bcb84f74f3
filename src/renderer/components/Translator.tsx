import React, { useState, useEffect, useRef, useCallback } from 'react';

interface LibreTranslateResponse {
  translatedText: string;
}

export function Translator() {
  const [inputText, setInputText] = useState('');
  const [googleTranslation, setGoogleTranslation] = useState('');
  const [libreTranslation, setLibreTranslation] = useState('');
  const [isLoadingGoogle, setIsLoadingGoogle] = useState(false);
  const [isLoadingLibre, setIsLoadingLibre] = useState(false);
  const [fromLang, setFromLang] = useState('en');
  const [toLang, setToLang] = useState('zh');
  const [googleError, setGoogleError] = useState('');
  const [libreError, setLibreError] = useState('');
  const debounceRef = useRef<NodeJS.Timeout>();

  // Language options for LibreTranslate
  const languages = [
    { code: 'en', name: 'English' },
    { code: 'es', name: 'Spanish' },
    { code: 'fr', name: 'French' },
    { code: 'de', name: 'German' },
    { code: 'it', name: 'Italian' },
    { code: 'pt', name: 'Portuguese' },
    { code: 'ru', name: 'Russian' },
    { code: 'ja', name: 'Japanese' },
    { code: 'ko', name: 'Korean' },
    { code: 'zh', name: 'Chinese' },
    { code: 'ar', name: 'Arabic' },
    { code: 'hi', name: 'Hindi' }
  ];

  // Google Translate using unofficial API
  const translateWithGoogle = useCallback(async () => {
    if (!inputText.trim() || fromLang === toLang) {
      setGoogleTranslation('');
      return;
    }

    setIsLoadingGoogle(true);
    setGoogleError('');

    try {
      // Using Google Translate unofficial API
      const response = await fetch(
        `https://translate.googleapis.com/translate_a/single?client=gtx&sl=${fromLang}&tl=${toLang}&dt=t&q=${encodeURIComponent(inputText)}`
      );
      
      if (!response.ok) {
        throw new Error('Google Translate request failed');
      }
      
      const data = await response.json();
      const translatedText = data[0]?.map((item: any) => item[0]).join('') || '';
      setGoogleTranslation(translatedText);
    } catch (err) {
      setGoogleError('Google Translate temporarily unavailable');
      console.error('Google Translate error:', err);
    } finally {
      setIsLoadingGoogle(false);
    }
  }, [inputText, fromLang, toLang]);

  const translateWithLibre = useCallback(async () => {
    if (!inputText.trim() || fromLang === toLang) {
      setLibreTranslation('');
      return;
    }

    setIsLoadingLibre(true);
    setLibreError('');

    try {
      // Try MyMemory Translation API (free, no key required)
      const response = await fetch(
        `https://api.mymemory.translated.net/get?q=${encodeURIComponent(inputText)}&langpair=${fromLang}|${toLang}`
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      if (data.responseStatus === 200) {
        setLibreTranslation(data.responseData.translatedText);
      } else {
        throw new Error('Translation failed');
      }
    } catch (err) {
      setLibreError('Alternative translation service unavailable');
      console.error('MyMemory translation error:', err);
    } finally {
      setIsLoadingLibre(false);
    }
  }, [inputText, fromLang, toLang]);

  // Debounced translation for both services
  useEffect(() => {
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }

    if (!inputText.trim()) {
      setGoogleTranslation('');
      setLibreTranslation('');
      return;
    }

    debounceRef.current = setTimeout(() => {
      translateWithGoogle();
      translateWithLibre();
    }, 500);

    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }
    };
  }, [inputText, fromLang, toLang, translateWithGoogle, translateWithLibre]);

  const handleCopy = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      // You could add a toast notification here
    } catch (err) {
      console.error('Failed to copy text:', err);
    }
  };

  const handleSpeak = (text: string, lang: string) => {
    if ('speechSynthesis' in window) {
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.lang = lang === 'zh' ? 'zh-CN' : lang;
      speechSynthesis.speak(utterance);
    }
  };

  const clearInput = () => {
    setInputText('');
    setGoogleTranslation('');
    setLibreTranslation('');
    setGoogleError('');
    setLibreError('');
  };

  const characterCount = inputText.length;
  const maxCharacters = 5000;

  return (
    <div className="h-full flex bg-white" style={{ backgroundColor: 'var(--bg-primary)' }}>
      {/* Input Panel - Left Half */}
      <div className="w-1/2 border-r border-gray-200 p-6 flex flex-col" style={{ borderColor: 'var(--border-color)' }}>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-semibold" style={{ color: 'var(--text-primary)' }}>
            Translator
          </h2>
          <button
            onClick={clearInput}
            className="px-3 py-1 text-sm border rounded-md hover:bg-gray-50 transition-colors"
            style={{ 
              color: 'var(--text-secondary)', 
              borderColor: 'var(--border-color)',
              backgroundColor: 'transparent'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = 'var(--bg-secondary)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent';
            }}
          >
            Clear
          </button>
        </div>

        {/* Language Selection */}
        <div className="flex items-center gap-4 mb-4">
          <div className="flex items-center gap-2">
            <label className="text-sm font-medium" style={{ color: 'var(--text-secondary)' }}>From:</label>
            <select
              value={fromLang}
              onChange={(e) => setFromLang(e.target.value)}
              className="px-2 py-1 border rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              style={{ 
                backgroundColor: 'var(--bg-primary)', 
                borderColor: 'var(--border-color)', 
                color: 'var(--text-primary)' 
              }}
            >
              {languages.map((lang) => (
                <option key={lang.code} value={lang.code}>
                  {lang.name}
                </option>
              ))}
            </select>
          </div>
          
          <button
            onClick={() => {
              const temp = fromLang;
              setFromLang(toLang);
              setToLang(temp);
            }}
            className="p-1 rounded hover:bg-gray-100 transition-colors"
            style={{ color: 'var(--text-secondary)' }}
            title="Swap languages"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
            </svg>
          </button>
          
          <div className="flex items-center gap-2">
            <label className="text-sm font-medium" style={{ color: 'var(--text-secondary)' }}>To:</label>
            <select
              value={toLang}
              onChange={(e) => setToLang(e.target.value)}
              className="px-2 py-1 border rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              style={{ 
                backgroundColor: 'var(--bg-primary)', 
                borderColor: 'var(--border-color)', 
                color: 'var(--text-primary)' 
              }}
            >
              {languages.map((lang) => (
                <option key={lang.code} value={lang.code}>
                  {lang.name}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Input Text Area */}
        <div className="flex-1 flex flex-col">
          <textarea
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            placeholder="Enter text to translate..."
            className="flex-1 p-4 border rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
            style={{ 
              backgroundColor: 'var(--bg-primary)', 
              borderColor: 'var(--border-color)', 
              color: 'var(--text-primary)' 
            }}
            maxLength={maxCharacters}
          />
          
          {/* Character Count */}
          <div className="flex justify-between items-center mt-2">
            <span className="text-xs" style={{ color: 'var(--text-secondary)' }}>
              {characterCount}/{maxCharacters} characters
            </span>
            {inputText && (
              <div className="flex gap-2">
                <button
                  onClick={() => handleSpeak(inputText, fromLang)}
                  className="p-1 rounded hover:bg-gray-100 transition-colors"
                  style={{ color: 'var(--text-secondary)' }}
                  title="Listen to pronunciation"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M9 12h6l-3-3v6l-3-3z" />
                  </svg>
                </button>
                <button
                  onClick={() => handleCopy(inputText)}
                  className="p-1 rounded hover:bg-gray-100 transition-colors"
                  style={{ color: 'var(--text-secondary)' }}
                  title="Copy text"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Results Panel - Right Half */}
      <div className="w-1/2 flex flex-col">
        {/* Google Translate - Top Right */}
        <div className="h-1/2 border-b p-6 flex flex-col" style={{ borderColor: 'var(--border-color)' }}>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold" style={{ color: 'var(--text-primary)' }}>
              Google Translate
            </h3>
            {isLoadingGoogle && (
              <div className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                Translating...
              </div>
            )}
          </div>
          
          <div className="flex-1 flex flex-col min-h-0">
            <div 
              className="flex-1 p-3 border rounded-lg overflow-y-auto"
              style={{ 
                backgroundColor: 'var(--bg-secondary)', 
                borderColor: 'var(--border-color)', 
                color: 'var(--text-primary)' 
              }}
            >
              {googleError ? (
                <div className="text-red-500 text-sm break-words">{googleError}</div>
              ) : googleTranslation ? (
                <div className="whitespace-pre-wrap break-words">{googleTranslation}</div>
              ) : (
                <div className="text-gray-400 italic">Google translation will appear here...</div>
              )}
            </div>

            {/* Google Translate Actions */}
            {googleTranslation && !googleError && (
              <div className="flex justify-end gap-2 mt-2 flex-shrink-0">
                <button
                  onClick={() => handleSpeak(googleTranslation, toLang)}
                  className="p-1 rounded hover:bg-gray-100 transition-colors"
                  style={{ color: 'var(--text-secondary)' }}
                  title="Listen to pronunciation"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M9 12h6l-3-3v6l-3-3z" />
                  </svg>
                </button>
                <button
                  onClick={() => handleCopy(googleTranslation)}
                  className="p-1 rounded hover:bg-gray-100 transition-colors"
                  style={{ color: 'var(--text-secondary)' }}
                  title="Copy translation"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                </button>
              </div>
            )}
          </div>
        </div>

        {/* MyMemory Translation - Bottom Right */}
        <div className="h-1/2 border-b p-6 flex flex-col" style={{ borderColor: 'var(--border-color)' }}>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold" style={{ color: 'var(--text-primary)' }}>
              MyMemory Translation
            </h3>
            {isLoadingLibre && (
              <div className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                Translating...
              </div>
            )}
          </div>

          <div className="flex-1 flex flex-col min-h-0">
            <div 
              className="flex-1 p-3 border rounded-lg overflow-y-auto"
              style={{ 
                backgroundColor: 'var(--bg-secondary)', 
                borderColor: 'var(--border-color)', 
                color: 'var(--text-primary)' 
              }}
            >
              {libreError ? (
                <div className="text-red-500 text-sm break-words">{libreError}</div>
              ) : libreTranslation ? (
                <div className="whitespace-pre-wrap break-words">{libreTranslation}</div>
              ) : (
                <div className="text-gray-400 italic">Alternative translation will appear here...</div>
              )}
            </div>

            {/* MyMemory Translation Actions */}
            {libreTranslation && !libreError && (
              <div className="flex justify-end gap-2 mt-2 flex-shrink-0">
                <button
                  onClick={() => handleSpeak(libreTranslation, toLang)}
                  className="p-1 rounded hover:bg-gray-100 transition-colors"
                  style={{ color: 'var(--text-secondary)' }}
                  title="Listen to pronunciation"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M9 12h6l-3-3v6l-3-3z" />
                  </svg>
                </button>
                <button
                  onClick={() => handleCopy(libreTranslation)}
                  className="p-1 rounded hover:bg-gray-100 transition-colors"
                  style={{ color: 'var(--text-secondary)' }}
                  title="Copy translation"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}