import React, { useState, useRef, useEffect } from 'react';
import { NoteNode } from '../../shared/types';

interface TreeNodeProps {
  node: NoteNode;
  level: number;
  selectedNodeId?: string;
  editingNodeId?: string;
  newlyCreatedNodeId?: string;
  onNodeSelect: (node: NoteNode) => void;
  onToggleFolder: (folderId: string) => void;
  onCreateNote: (parentId: string) => void;
  onCreateFolder: (parentId: string) => void;
  onDeleteNode: (nodeId: string) => void;
  onRenameNode: (nodeId: string, newName: string) => void;
  onStartEdit: (nodeId: string) => void;
  onFinishEdit: (nodeId: string, newName: string) => void;
  onCancelEdit: (nodeId: string) => void;
  onDragStart?: (nodeId: string) => void;
  onDragOver?: (targetId: string, position: 'above' | 'below' | 'inside') => void;
  onDrop?: (draggedId: string, targetId: string, position: 'above' | 'below' | 'inside') => void;
}

export function TreeNode({
  node,
  level,
  selectedNodeId,
  editingNodeId,
  newlyCreatedNodeId,
  onNodeSelect,
  onToggleFolder,
  onCreateNote,
  onCreateFolder,
  onDeleteNode,
  onRenameNode,
  onStartEdit,
  onFinishEdit,
  onCancelEdit,
  onDragStart,
  onDragOver,
  onDrop
}: TreeNodeProps) {
  const [editValue, setEditValue] = useState(node.title || '');
  const [showContextMenu, setShowContextMenu] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [dragOver, setDragOver] = useState<'above' | 'below' | 'inside' | null>(null);
  const [isComposing, setIsComposing] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const nodeRef = useRef<HTMLDivElement>(null);
  
  const isEditing = editingNodeId === node.id;
  const isSelected = selectedNodeId === node.id;
  const isNewlyCreated = newlyCreatedNodeId === node.id;

  // Debug logging for state changes
  useEffect(() => {
    if (isEditing) {
      console.log(`TreeNode ${node.id} (${node.title}) entered edit mode`);
    }
  }, [isEditing, node.id, node.title]);

  useEffect(() => {
    if (isNewlyCreated) {
      console.log(`TreeNode ${node.id} (${node.title}) marked as newly created`);
    }
  }, [isNewlyCreated, node.id, node.title]);

  // Auto-focus and select all when entering edit mode
  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      inputRef.current.select();
    }
  }, [isEditing]);

  // Update edit value when node title changes
  useEffect(() => {
    setEditValue(node.title || '');
  }, [node.title]);

  const handleToggleFolder = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (node.isFolder) {
      onToggleFolder(node.id);
    }
  };

  const handleNodeClick = () => {
    if (!isEditing) {
      if (!node.isFolder) {
        onNodeSelect(node);
      }
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (isEditing) return; // Don't handle shortcuts while editing
    
    if (e.key === 'F2') {
      e.preventDefault();
      onStartEdit(node.id);
    } else if (e.key === 'Delete' || e.key === 'Backspace') {
      e.preventDefault();
      const confirmMessage = node.isFolder 
        ? `Warning: This will delete the folder "${node.title}" and all notes inside it. Please make sure to save any important data. Are you sure you want to continue?`
        : `Are you sure you want to delete "${node.title}"?`;
      if (confirm(confirmMessage)) {
        onDeleteNode(node.id);
      }
    } else if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      handleNodeClick();
    }
  };

  const handleDoubleClick = (e: React.MouseEvent) => {
    // Only trigger rename when double-clicking on the text/title area, not on buttons
    if (!isEditing && e.target instanceof HTMLElement) {
      const target = e.target as HTMLElement;
      // Check if the double-click happened on a button or expand/collapse control
      if (target.tagName === 'BUTTON' || target.closest('button')) {
        return; // Don't start editing if clicked on a button
      }
      onStartEdit(node.id);
    }
  };

  const handleInputKeyDown = (e: React.KeyboardEvent) => {
    // Don't process Enter key during IME composition (for Chinese/Japanese input)
    if (isComposing || e.nativeEvent.isComposing || e.key === 'Process') {
      return;
    }
    
    if (e.key === 'Enter') {
      e.preventDefault();
      if (editValue.trim()) {
        onFinishEdit(node.id, editValue.trim());
      } else {
        onCancelEdit(node.id);
      }
    } else if (e.key === 'Escape') {
      e.preventDefault();
      setEditValue(node.title || '');
      onCancelEdit(node.id);
    }
  };

  const handleCompositionStart = () => {
    setIsComposing(true);
  };

  const handleCompositionEnd = () => {
    setIsComposing(false);
  };

  const handleBlur = () => {
    if (editValue.trim()) {
      onFinishEdit(node.id, editValue.trim());
    } else {
      onCancelEdit(node.id);
    }
  };

  const handleContextMenu = (e: React.MouseEvent) => {
    e.preventDefault();
    setShowContextMenu(true);
  };

  // Drag and drop handlers
  const handleDragStart = (e: React.DragEvent) => {
    if (isEditing) {
      e.preventDefault();
      return;
    }
    
    setIsDragging(true);
    e.dataTransfer.setData('text/plain', node.id);
    e.dataTransfer.effectAllowed = 'move';
    
    if (onDragStart) {
      onDragStart(node.id);
    }
  };

  const handleDragEnd = () => {
    setIsDragging(false);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';

    if (!nodeRef.current) return;

    const rect = nodeRef.current.getBoundingClientRect();
    const y = e.clientY - rect.top;
    const height = rect.height;

    let position: 'above' | 'below' | 'inside';
    
    if (node.isFolder && y > height * 0.25 && y < height * 0.75) {
      position = 'inside';
    } else if (y < height / 2) {
      position = 'above';
    } else {
      position = 'below';
    }

    setDragOver(position);
    
    if (onDragOver) {
      onDragOver(node.id, position);
    }
  };

  const handleDragLeave = (e: React.DragEvent) => {
    // Only clear drag over if we're actually leaving the element
    if (!nodeRef.current?.contains(e.relatedTarget as Node)) {
      setDragOver(null);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    const draggedId = e.dataTransfer.getData('text/plain');
    
    if (draggedId && draggedId !== node.id && dragOver && onDrop) {
      onDrop(draggedId, node.id, dragOver);
    }
    
    setDragOver(null);
  };

  const indentStyle = {
    paddingLeft: `${level * 16 + 8}px`
  };

  // Node icon based on state
  const getNodeIcon = () => {
    if (isEditing) {
      return (
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="text-blue-500">
          <path d="M11 4H4a2 2 0 00-2 2v14a2 2 0 002 2h14a2 2 0 002-2v-7"/>
          <path d="M18.5 2.5a2.121 2.121 0 013 3L12 15l-4 1 1-4 9.5-9.5z"/>
        </svg>
      );
    }
    if (node.isTemporary) {
      return (
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="text-yellow-500">
          <circle cx="12" cy="12" r="10"/>
          <polyline points="12,6 12,12 16,14"/>
        </svg>
      );
    }
    if (node.isFolder) {
      return (
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="text-blue-500">
          <path d="M22 19a2 2 0 01-2 2H4a2 2 0 01-2-2V5a2 2 0 012-2h5l2 3h9a2 2 0 012 2z"/>
        </svg>
      );
    }
    return (
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="text-gray-400">
        <path d="M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8z"/>
        <polyline points="14,2 14,8 20,8"/>
        <line x1="16" y1="13" x2="8" y2="13"/>
        <line x1="16" y1="17" x2="8" y2="17"/>
        <polyline points="10,9 9,9 8,9"/>
      </svg>
    );
  };

  return (
    <>
      <div
        ref={nodeRef}
        className={`flex items-center py-1 px-2 transition-colors group relative select-none ${
          !isEditing ? 'cursor-pointer hover:bg-gray-100' : ''
        } ${
          isSelected ? 'bg-nexus-50 text-nexus-600' : 'text-gray-700'
        } ${
          isEditing ? 'editing-item' : ''
        } ${
          node.isTemporary && !isEditing ? 'temporary-item' : ''
        } ${
          isNewlyCreated && !isEditing ? 'newly-created' : ''
        } ${
          isDragging ? 'opacity-50' : ''
        } ${
          dragOver === 'above' ? 'border-t-2 border-blue-500' : ''
        } ${
          dragOver === 'below' ? 'border-b-2 border-blue-500' : ''
        } ${
          dragOver === 'inside' ? 'bg-blue-50 border border-blue-300' : ''
        }`}
        style={indentStyle}
        tabIndex={0}
        draggable={!isEditing}
        onClick={handleNodeClick}
        onDoubleClick={handleDoubleClick}
        onContextMenu={handleContextMenu}
        onKeyDown={handleKeyDown}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        {/* Expand/Collapse Icon */}
        {node.isFolder && (
          <button
            onClick={(e) => {
              console.log('🔧 TreeNode button clicked:', node.title, 'isExpanded:', node.isExpanded, 'type:', typeof node.isExpanded);
              handleToggleFolder(e);
            }}
            className="mr-1 p-0.5 w-4 h-4 flex items-center justify-center rounded hover:bg-gray-200 transition-colors text-xs font-bold"
          >
            {(() => {
              const symbol = node.isExpanded ? '−' : '+';
              console.log('🔧 TreeNode render:', node.title, 'isExpanded:', node.isExpanded, 'showing:', symbol);
              return symbol;
            })()}
          </button>
        )}

        {/* Node Icon */}
        <div className="mr-2 flex-shrink-0">
          {getNodeIcon()}
        </div>

        {/* Title or Input */}
        {isEditing ? (
          <input
            ref={inputRef}
            type="text"
            value={editValue}
            onChange={(e) => setEditValue(e.target.value)}
            onKeyDown={handleInputKeyDown}
            onBlur={handleBlur}
            onCompositionStart={handleCompositionStart}
            onCompositionEnd={handleCompositionEnd}
            className="inline-edit-input flex-1"
            maxLength={255}
            autoComplete="off"
            spellCheck={false}
          />
        ) : (
          <span
            className={`flex-1 text-sm truncate ${node.isPinned ? 'font-semibold' : ''} ${
              node.isTemporary ? 'text-gray-500 italic' : ''
            } ${!node.title ? 'text-gray-400 italic' : ''}`}
          >
            {node.title || 'Untitled'}
            {node.isPinned && (
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="ml-1 text-yellow-500 inline">
                <path d="M12 17v5"/>
                <path d="M9 10.76a2 2 0 0 1-1.11 1.79l-1.78.9A2 2 0 0 0 7 15.24V16a1 1 0 0 0 1 1h8a1 1 0 0 0 1-1v-.76a2 2 0 0 0 .89-1.79l-1.78-.9A2 2 0 0 1 15 10.76V7a1 1 0 0 0-1-1H10a1 1 0 0 0-1 1v3.76Z"/>
              </svg>
            )}
          </span>
        )}

        {/* Context Menu Trigger - only show when not editing */}
        {!isEditing && (
          <button
            onClick={(e) => {
              e.stopPropagation();
              setShowContextMenu(!showContextMenu);
            }}
            className="opacity-0 group-hover:opacity-100 p-1 rounded hover:bg-gray-200 transition-all"
          >
            <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
            </svg>
          </button>
        )}

        {/* Context Menu */}
        {showContextMenu && !isEditing && (
          <div className="absolute right-0 top-full mt-1 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
            {node.isFolder ? (
              <>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onCreateNote(node.id);
                    setShowContextMenu(false);
                  }}
                  className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 flex items-center"
                >
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="mr-2">
                    <path d="M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8z"/>
                    <polyline points="14,2 14,8 20,8"/>
                    <line x1="16" y1="13" x2="8" y2="13"/>
                    <line x1="16" y1="17" x2="8" y2="17"/>
                    <polyline points="10,9 9,9 8,9"/>
                  </svg>
                  New Note
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onCreateFolder(node.id);
                    setShowContextMenu(false);
                  }}
                  className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 flex items-center"
                >
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="mr-2">
                    <path d="M22 19a2 2 0 01-2 2H4a2 2 0 01-2-2V5a2 2 0 012-2h5l2 3h9a2 2 0 012 2z"/>
                  </svg>
                  New Folder
                </button>
                <hr className="my-1" />
              </>
            ) : null}
            <button
              onClick={(e) => {
                e.stopPropagation();
                onStartEdit(node.id);
                setShowContextMenu(false);
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 flex items-center"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="mr-2">
                <path d="M11 4H4a2 2 0 00-2 2v14a2 2 0 002 2h14a2 2 0 002-2v-7"/>
                <path d="M18.5 2.5a2.121 2.121 0 013 3L12 15l-4 1 1-4 9.5-9.5z"/>
              </svg>
              Rename
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                const confirmMessage = node.isFolder 
                  ? `Warning: This will delete the folder "${node.title}" and all notes inside it. Please make sure to save any important data. Are you sure you want to continue?`
                  : `Are you sure you want to delete "${node.title}"?`;
                if (confirm(confirmMessage)) {
                  onDeleteNode(node.id);
                }
                setShowContextMenu(false);
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-red-50 text-red-600 flex items-center"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="mr-2">
                <polyline points="3,6 5,6 21,6"/>
                <path d="M19,6V20a2,2,0,0,1-2,2H7a2,2,0,0,1-2-2V6M8,6V4a2,2,0,0,1,2-2h4a2,2,0,0,1,2,2V6"/>
                <line x1="10" y1="11" x2="10" y2="17"/>
                <line x1="14" y1="11" x2="14" y2="17"/>
              </svg>
              Delete
            </button>
          </div>
        )}
      </div>

      {/* Click outside to close context menu */}
      {showContextMenu && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setShowContextMenu(false)}
        />
      )}

      {/* Render children */}
      {node.isFolder && node.isExpanded && node.children.length > 0 && (
        <div>
          {node.children.map((child) => (
            <TreeNode
              key={child.id}
              node={child}
              level={level + 1}
              selectedNodeId={selectedNodeId}
              editingNodeId={editingNodeId}
              newlyCreatedNodeId={newlyCreatedNodeId}
              onNodeSelect={onNodeSelect}
              onToggleFolder={onToggleFolder}
              onCreateNote={onCreateNote}
              onCreateFolder={onCreateFolder}
              onDeleteNode={onDeleteNode}
              onRenameNode={onRenameNode}
              onStartEdit={onStartEdit}
              onFinishEdit={onFinishEdit}
              onCancelEdit={onCancelEdit}
              onDragStart={onDragStart}
              onDragOver={onDragOver}
              onDrop={onDrop}
            />
          ))}
        </div>
      )}
    </>
  );
}