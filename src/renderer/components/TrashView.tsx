import React, { useState, useEffect, useCallback } from 'react';
import { Note } from '../../shared/types';

interface TrashViewProps {
  searchQuery: string;
}

export function TrashView({ searchQuery }: TrashViewProps) {
  const [trashedNotes, setTrashedNotes] = useState<Note[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedNotes, setSelectedNotes] = useState<Set<string>>(new Set());

  // Load trashed notes
  const loadTrashedNotes = useCallback(async () => {
    if (!window.electronAPI) return;
    
    try {
      setIsLoading(true);
      const notes = await window.electronAPI.getTrashedNotes();
      setTrashedNotes(notes);
    } catch (error) {
      console.error('Error loading trashed notes:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Load trashed notes on mount
  useEffect(() => {
    loadTrashedNotes();
  }, [loadTrashedNotes]);

  // Filter notes based on search query
  const filteredNotes = trashedNotes.filter(note => {
    if (!searchQuery.trim()) return true;
    const query = searchQuery.toLowerCase();
    return (
      note.title?.toLowerCase().includes(query) ||
      note.content.toLowerCase().includes(query)
    );
  });

  // Restore single note
  const handleRestoreNote = useCallback(async (noteId: string) => {
    if (!window.electronAPI) return;
    
    try {
      await window.electronAPI.restoreNote(noteId);
      await loadTrashedNotes(); // Refresh the list
      
      // Show success message
      showNotification('Note restored successfully', 'success');
    } catch (error) {
      console.error('Error restoring note:', error);
      showNotification('Failed to restore note', 'error');
    }
  }, [loadTrashedNotes]);

  // Permanently delete single note
  const handlePermanentDelete = useCallback(async (noteId: string, noteTitle: string) => {
    if (!window.electronAPI) return;
    
    if (!confirm(`Are you sure you want to permanently delete "${noteTitle}"? This cannot be undone.`)) {
      return;
    }
    
    try {
      await window.electronAPI.permanentlyDeleteNote(noteId);
      await loadTrashedNotes(); // Refresh the list
      
      showNotification('Note permanently deleted', 'success');
    } catch (error) {
      console.error('Error permanently deleting note:', error);
      showNotification('Failed to delete note permanently', 'error');
    }
  }, [loadTrashedNotes]);

  // Restore selected notes
  const handleRestoreSelected = useCallback(async () => {
    if (!window.electronAPI || selectedNotes.size === 0) return;
    
    try {
      const promises = Array.from(selectedNotes).map(noteId => 
        window.electronAPI.restoreNote(noteId)
      );
      await Promise.all(promises);
      
      setSelectedNotes(new Set());
      await loadTrashedNotes();
      
      showNotification(`${selectedNotes.size} notes restored successfully`, 'success');
    } catch (error) {
      console.error('Error restoring selected notes:', error);
      showNotification('Failed to restore selected notes', 'error');
    }
  }, [selectedNotes, loadTrashedNotes]);

  // Empty entire trash
  const handleEmptyTrash = useCallback(async () => {
    if (!window.electronAPI || trashedNotes.length === 0) return;
    
    if (!confirm(`Are you sure you want to permanently delete all ${trashedNotes.length} notes in trash? This cannot be undone.`)) {
      return;
    }
    
    try {
      const deletedCount = await window.electronAPI.emptyTrash();
      await loadTrashedNotes();
      
      showNotification(`${deletedCount} notes permanently deleted`, 'success');
    } catch (error) {
      console.error('Error emptying trash:', error);
      showNotification('Failed to empty trash', 'error');
    }
  }, [trashedNotes.length, loadTrashedNotes]);

  // Toggle note selection
  const handleToggleSelect = useCallback((noteId: string) => {
    setSelectedNotes(prev => {
      const newSet = new Set(prev);
      if (newSet.has(noteId)) {
        newSet.delete(noteId);
      } else {
        newSet.add(noteId);
      }
      return newSet;
    });
  }, []);

  // Select all filtered notes
  const handleSelectAll = useCallback(() => {
    const allFilteredIds = new Set(filteredNotes.map(note => note.id));
    setSelectedNotes(allFilteredIds);
  }, [filteredNotes]);

  // Clear selection
  const handleClearSelection = useCallback(() => {
    setSelectedNotes(new Set());
  }, []);

  // Simple notification function (could be enhanced with a toast library)
  const showNotification = (message: string, type: 'success' | 'error') => {
    // For now, just use console - in a real app, you'd use a toast notification
    console.log(`${type.toUpperCase()}: ${message}`);
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-gray-500">Loading trash...</div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <h2 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <polyline points="3,6 5,6 21,6"/>
                <path d="M19,6V20a2,2,0,0,1-2,2H7a2,2,0,0,1-2-2V6M8,6V4a2,2,0,0,1,2-2h4a2,2,0,0,1,2,2V6"/>
                <line x1="10" y1="11" x2="10" y2="17"/>
                <line x1="14" y1="11" x2="14" y2="17"/>
              </svg>
              Trash ({filteredNotes.length})
            </h2>
            {selectedNotes.size > 0 && (
              <span className="text-sm text-gray-500">
                {selectedNotes.size} selected
              </span>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            {selectedNotes.size > 0 && (
              <>
                <button
                  onClick={handleRestoreSelected}
                  className="btn-secondary text-sm py-1 px-3"
                >
                  Restore Selected
                </button>
                <button
                  onClick={handleClearSelection}
                  className="btn-ghost text-sm py-1 px-2"
                >
                  Clear
                </button>
              </>
            )}
            
            {filteredNotes.length > 0 && (
              <>
                <button
                  onClick={selectedNotes.size === filteredNotes.length ? handleClearSelection : handleSelectAll}
                  className="btn-ghost text-sm py-1 px-2"
                >
                  {selectedNotes.size === filteredNotes.length ? 'Deselect All' : 'Select All'}
                </button>
                <button
                  onClick={handleEmptyTrash}
                  className="btn-secondary text-sm py-1 px-3 text-red-600 hover:bg-red-50"
                >
                  Empty Trash
                </button>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto">
        {filteredNotes.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-gray-500">
            <svg className="w-16 h-16 mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
            <p className="text-lg mb-2">
              {searchQuery ? `No trashed notes match "${searchQuery}"` : 'Trash is empty'}
            </p>
            <p className="text-sm">
              {searchQuery ? 'Try a different search term' : 'Deleted notes will appear here'}
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-100">
            {filteredNotes.map((note) => (
              <div
                key={note.id}
                className={`p-4 hover:bg-gray-50 transition-colors ${
                  selectedNotes.has(note.id) ? 'bg-blue-50 border-l-4 border-l-blue-400' : ''
                }`}
              >
                <div className="flex items-start space-x-3">
                  {/* Checkbox */}
                  <input
                    type="checkbox"
                    checked={selectedNotes.has(note.id)}
                    onChange={() => handleToggleSelect(note.id)}
                    className="mt-1 h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                  />
                  
                  {/* Note Content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <h3 className="text-sm font-medium text-gray-900 truncate">
                        {note.title || ''}
                        {note.is_folder && (
                          <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="ml-1 inline">
                            <path d="M22 19a2 2 0 01-2 2H4a2 2 0 01-2-2V5a2 2 0 012-2h5l2 3h9a2 2 0 012 2z"/>
                          </svg>
                        )}
                      </h3>
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handleRestoreNote(note.id)}
                          className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                        >
                          Restore
                        </button>
                        <button
                          onClick={() => handlePermanentDelete(note.id, note.title || '')}
                          className="text-red-600 hover:text-red-800 text-sm font-medium"
                        >
                          Delete Forever
                        </button>
                      </div>
                    </div>
                    
                    {note.content && (
                      <p className="mt-1 text-sm text-gray-600 line-clamp-2">
                        {note.content}
                      </p>
                    )}
                    
                    <div className="mt-2 flex items-center space-x-4 text-xs text-gray-500">
                      <span>
                        Deleted: {note.deleted_at ? formatDate(note.deleted_at) : 'Unknown'}
                      </span>
                      <span>
                        Created: {formatDate(note.created_at)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}