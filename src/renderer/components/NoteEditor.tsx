import React, { useState, useEffect, useRef } from 'react';
import { Note } from '../../shared/types';
import { useAutoSave } from '../hooks/useAutoSave';
import { SaveStatusIndicator } from './SaveStatusIndicator';
import { FindReplacePanel } from './FindReplacePanel';
import { MarkdownPreview } from './MarkdownPreview';

// Global scroll position storage for notes
const noteScrollPositions = new Map<string, number>();

// Function to detect if text contains markdown syntax
const hasMarkdownSyntax = (text: string): boolean => {
  if (!text || text.trim().length === 0) return false;
  
  // Common markdown patterns
  const markdownPatterns = [
    /^#{1,6}\s+.+$/m,           // Headers (# ## ### etc)
    /\*\*[^*]+\*\*/,            // Bold **text**
    /\*[^*]+\*/,                // Italic *text*
    /__[^_]+__/,                // Bold __text__
    /_[^_]+_/,                  // Italic _text_
    /\[([^\]]+)\]\([^)]+\)/,    // Links [text](url)
    /^[-*+]\s+.+$/m,            // Unordered lists
    /^\d+\.\s+.+$/m,            // Ordered lists
    /^```[\s\S]*?```$/m,        // Code blocks
    /`[^`]+`/,                  // Inline code
    /^>\s+.+$/m,                // Blockquotes
    /^\|.+\|$/m,                // Tables
    /^---+$/m,                  // Horizontal rules
    /!\[([^\]]*)\]\([^)]+\)/,   // Images ![alt](src)
    /~~[^~]+~~/,                // Strikethrough ~~text~~
  ];
  
  return markdownPatterns.some(pattern => pattern.test(text));
};

interface NoteEditorProps {
  note: Note;
  onUpdateNote: (id: string, content: string, title?: string) => Promise<void>;
}

export function NoteEditor({ note, onUpdateNote }: NoteEditorProps) {
  const [editorContent, setEditorContent] = useState(note.content);
  const [showFindReplace, setShowFindReplace] = useState(false);
  const [isMarkdownPreview, setIsMarkdownPreview] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const previousNoteId = useRef<string>(note.id);

  // Check if content has markdown syntax
  const hasMarkdown = hasMarkdownSyntax(editorContent);

  // Update editor when note changes
  useEffect(() => {
    // Save scroll position of the previous note
    if (previousNoteId.current && textareaRef.current && previousNoteId.current !== note.id) {
      noteScrollPositions.set(previousNoteId.current, textareaRef.current.scrollTop);
    }
    
    setEditorContent(note.content);
    
    // Reset preview mode when switching notes
    if (note.id !== previousNoteId.current) {
      setIsMarkdownPreview(false);
      
      // Restore scroll position for the new note or focus if it's a new empty note
      setTimeout(() => {
        if (textareaRef.current) {
          const savedScrollPosition = noteScrollPositions.get(note.id) || 0;
          textareaRef.current.scrollTop = savedScrollPosition;
          
          // Focus the editor if this is a new note (empty content and no title)
          const isNewNote = !note.content.trim() && (!note.title || !note.title.trim());
          if (isNewNote) {
            textareaRef.current.focus();
            console.log('Focused new note editor');
          }
        }
      }, 0);
      
      previousNoteId.current = note.id;
    }
  }, [note.id, note.content]);

  // Auto-save functionality - only for content, not title
  const autoSave = useAutoSave(editorContent, note.title || '', {
    noteId: note.id,
    delay: 3000,
    onSave: async (noteId: string, content: string) => {
      await onUpdateNote(noteId, content);
    },
    isEditing: true // Enable auto-save
  });

  // Handle keyboard shortcuts for find/replace
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.metaKey && (e.key === 'f' || e.key === 'h')) {
        e.preventDefault();
        setShowFindReplace(true);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  // Save scroll position periodically while user is interacting with the note
  useEffect(() => {
    const saveScrollPosition = () => {
      if (textareaRef.current) {
        noteScrollPositions.set(note.id, textareaRef.current.scrollTop);
      }
    };

    const handleScroll = () => {
      saveScrollPosition();
    };

    const textarea = textareaRef.current;
    if (textarea) {
      textarea.addEventListener('scroll', handleScroll);
      return () => textarea.removeEventListener('scroll', handleScroll);
    }
  }, [note.id]);

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="flex flex-col h-full relative" style={{ backgroundColor: 'var(--bg-primary)' }}>
      {/* Editor Header */}
      <div className="editor-header flex-shrink-0">
        <div className="flex items-center justify-between w-full">
          <div>
            <h1 
              className="note-title-readonly editor-title"
              title={note.title || ''}
              style={{ 
                color: 'var(--text-primary)',
                fontSize: '16px',
                fontWeight: '600',
                margin: 0,
                padding: 0,
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                maxWidth: '300px',
                cursor: 'default'
              }}
            >
              {note.title || ''}
            </h1>
          </div>
          <div className="flex items-center">
            {/* Save Status Indicator */}
            <div style={{ marginRight: '8px' }}>
              <SaveStatusIndicator 
                status={autoSave.saveStatus}
                lastSaved={autoSave.lastSaved}
              />
            </div>
            
            <button
              className="search-in-note-btn"
              onClick={() => !isMarkdownPreview && setShowFindReplace(true)}
              disabled={isMarkdownPreview}
              title={isMarkdownPreview ? "Find is not available in preview mode" : "Find in note (⌘F)"}
              style={{
                background: 'none',
                border: 'none',
                fontSize: '14px',
                cursor: isMarkdownPreview ? 'not-allowed' : 'pointer',
                padding: '4px',
                borderRadius: '4px',
                color: isMarkdownPreview ? 'var(--text-tertiary)' : 'var(--text-secondary)',
                marginRight: '8px',
                opacity: isMarkdownPreview ? 0.5 : 1
              }}
              onMouseEnter={(e) => {
                if (!isMarkdownPreview) {
                  e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
                }
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent';
              }}
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <circle cx="11" cy="11" r="8"/>
                <path d="m21 21-4.35-4.35"/>
                <path d="M11 8v6"/>
                <path d="M8 11h6"/>
              </svg>
            </button>
            
            <button
              className="markdown-preview-btn"
              onClick={() => setIsMarkdownPreview(!isMarkdownPreview)}
              disabled={!hasMarkdown}
              title={hasMarkdown ? 
                (isMarkdownPreview ? "Switch to edit mode" : "Preview markdown") : 
                "No markdown content detected"
              }
              style={{
                background: 'none',
                border: 'none',
                fontSize: '14px',
                cursor: hasMarkdown ? 'pointer' : 'not-allowed',
                padding: '4px',
                borderRadius: '4px',
                color: hasMarkdown ? 
                  (isMarkdownPreview ? 'var(--accent-color)' : 'var(--text-secondary)') : 
                  'var(--text-tertiary)',
                marginRight: '8px',
                opacity: hasMarkdown ? 1 : 0.3
              }}
              onMouseEnter={(e) => {
                if (hasMarkdown) {
                  e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
                }
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent';
              }}
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                <circle cx="12" cy="12" r="3"/>
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Editor Content */}
      <div 
        className="flex-1 p-4"
        style={{
          overflow: 'hidden', // Prevent this container from scrolling
          display: 'flex',
          flexDirection: 'column',
          position: 'relative'
        }}
      >
        {isMarkdownPreview ? (
          <MarkdownPreview 
            content={editorContent}
            style={{ 
              paddingBottom: '40px', // Add padding to prevent footer overlap
              padding: '0', // Override default padding since parent has padding
              height: '100%',
              flex: 1
            }}
          />
        ) : (
          <textarea
            ref={textareaRef}
            value={editorContent}
            onChange={(e) => setEditorContent(e.target.value)}
            onFocus={autoSave.startEditing}
            onBlur={autoSave.saveOnBlur}
            placeholder="Start writing..."
            className="editor-textarea"
            style={{ paddingBottom: '40px' }} // Add padding to prevent footer overlap
          />
        )}
      </div>

      {/* Editor Footer with Metadata and Save Status */}
      <div 
        className="editor-footer"
        style={{
          position: 'absolute',
          bottom: 0,
          right: 0,
          left: 0,
          height: '32px',
          background: 'var(--bg-primary)',
          borderTop: '1px solid var(--border-color)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'flex-end',
          padding: '0 16px'
        }}
      >
        <div className="note-metadata" style={{ display: 'flex', gap: '12px' }}>
          <span 
            className="metadata-item"
            style={{
              fontSize: '10px',
              color: 'var(--text-tertiary)',
              display: 'flex',
              alignItems: 'center',
              gap: '3px',
              opacity: 0.7
            }}
          >
            <svg width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <circle cx="12" cy="12" r="10"/>
              <polyline points="12,6 12,12 16,14"/>
            </svg>
            {formatDateTime(note.created_at)}
          </span>
          <span 
            className="metadata-item"
            style={{
              fontSize: '10px',
              color: 'var(--text-tertiary)',
              display: 'flex',
              alignItems: 'center',
              gap: '3px',
              opacity: 0.7
            }}
          >
            <svg width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M11 4H4a2 2 0 00-2 2v14a2 2 0 002 2h14a2 2 0 002-2v-7"/>
              <path d="M18.5 2.5a2.121 2.121 0 013 3L12 15l-4 1 1-4 9.5-9.5z"/>
            </svg>
            {formatDateTime(note.updated_at)}
          </span>
        </div>
        
      </div>

      {/* Find Replace Panel - only show in edit mode */}
      {!isMarkdownPreview && (
        <FindReplacePanel 
          isOpen={showFindReplace}
          onClose={() => setShowFindReplace(false)}
          textareaRef={textareaRef}
          content={editorContent}
          onContentChange={setEditorContent}
        />
      )}
    </div>
  );
}