import React, { useState, useEffect, useRef, useCallback } from 'react';

interface FindReplacePanelProps {
  isOpen: boolean;
  onClose: () => void;
  textareaRef?: React.RefObject<HTMLTextAreaElement>;
  content?: string;
  onContentChange?: (content: string) => void;
}

interface MatchPosition {
  index: number;
  length: number;
}

export function FindReplacePanel({ isOpen, onClose, textareaRef, content, onContentChange }: FindReplacePanelProps) {
  const [findText, setFindText] = useState('');
  const [replaceText, setReplaceText] = useState('');
  const [caseSensitive, setCaseSensitive] = useState(false);
  const [wholeWord, setWholeWord] = useState(false);
  const [currentMatchIndex, setCurrentMatchIndex] = useState(-1); // Current selection position
  const [totalMatches, setTotalMatches] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 60 });
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const findInputRef = useRef<HTMLInputElement>(null);
  const panelRef = useRef<HTMLDivElement>(null);

  // Custom undo system for text replacements
  const undoStackRef = useRef<Array<{content: string, selectionStart: number, selectionEnd: number}>>([]);
  const redoStackRef = useRef<Array<{content: string, selectionStart: number, selectionEnd: number}>>([]);

  const escapeRegex = (string: string) => {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  };

  // Find text starting from a given position (Notepad style)
  // Now accepts text as parameter to avoid stale content issues
  const findFromPosition = useCallback((text: string, startPos: number, direction: 'forward' | 'backward' = 'forward'): number => {
    if (!text || !findText) return -1;

    const searchText = caseSensitive ? findText : findText.toLowerCase();
    const targetText = caseSensitive ? text : text.toLowerCase();
    
    if (wholeWord) {
      const regex = new RegExp(`\\b${escapeRegex(searchText)}\\b`, caseSensitive ? 'g' : 'gi');
      if (direction === 'forward') {
        regex.lastIndex = startPos;
        const match = regex.exec(targetText);
        return match ? match.index : -1;
      } else {
        // For backward search, find all matches before startPos
        let lastMatchIndex = -1;
        let match;
        regex.lastIndex = 0;
        while ((match = regex.exec(targetText)) !== null && match.index < startPos) {
          lastMatchIndex = match.index;
        }
        return lastMatchIndex;
      }
    } else {
      if (direction === 'forward') {
        return targetText.indexOf(searchText, startPos);
      } else {
        return targetText.lastIndexOf(searchText, startPos - 1);
      }
    }
  }, [findText, caseSensitive, wholeWord, escapeRegex]);

  // Count total matches without storing positions
  const countMatchesInText = useCallback((text: string): number => {
    if (!findText || !text) return 0;

    const searchText = caseSensitive ? findText : findText.toLowerCase();
    const targetText = caseSensitive ? text : text.toLowerCase();
    let count = 0;
    
    if (wholeWord) {
      const regex = new RegExp(`\\b${escapeRegex(searchText)}\\b`, caseSensitive ? 'g' : 'gi');
      let match;
      while ((match = regex.exec(targetText)) !== null) {
        count++;
      }
    } else {
      let index = 0;
      while ((index = targetText.indexOf(searchText, index)) !== -1) {
        count++;
        index += searchText.length;
      }
    }
    
    return count;
  }, [findText, caseSensitive, wholeWord, escapeRegex]);

  // Save state for undo
  const saveUndoState = useCallback((textarea: HTMLTextAreaElement) => {
    undoStackRef.current.push({
      content: textarea.value,
      selectionStart: textarea.selectionStart,
      selectionEnd: textarea.selectionEnd
    });
    // Clear redo stack when new change is made
    redoStackRef.current = [];
    // Keep only last 50 states to prevent memory issues
    if (undoStackRef.current.length > 50) {
      undoStackRef.current.shift();
    }
  }, []);

  // Perform custom undo
  const performUndo = useCallback(() => {
    if (!textareaRef?.current || undoStackRef.current.length === 0) return false;
    
    const textarea = textareaRef.current;
    const lastState = undoStackRef.current.pop();
    
    if (lastState) {
      // Save current state to redo stack
      redoStackRef.current.push({
        content: textarea.value,
        selectionStart: textarea.selectionStart,
        selectionEnd: textarea.selectionEnd
      });
      
      // Restore previous state
      textarea.value = lastState.content;
      textarea.setSelectionRange(lastState.selectionStart, lastState.selectionEnd);
      
      // Notify React of the change
      const inputEvent = new Event('input', { bubbles: true });
      textarea.dispatchEvent(inputEvent);
      
      // Update the parent content if callback is available
      if (onContentChange) {
        onContentChange(lastState.content);
      }
      
      // Recalculate matches with new content
      const newCount = countMatchesInText(lastState.content);
      setTotalMatches(newCount);
      setCurrentMatchIndex(-1);
      
      return true;
    }
    
    return false;
  }, [textareaRef, onContentChange, countMatchesInText]);

  // Perform custom redo
  const performRedo = useCallback(() => {
    if (!textareaRef?.current || redoStackRef.current.length === 0) return false;
    
    const textarea = textareaRef.current;
    const redoState = redoStackRef.current.pop();
    
    if (redoState) {
      // Save current state to undo stack
      undoStackRef.current.push({
        content: textarea.value,
        selectionStart: textarea.selectionStart,
        selectionEnd: textarea.selectionEnd
      });
      
      // Restore redo state
      textarea.value = redoState.content;
      textarea.setSelectionRange(redoState.selectionStart, redoState.selectionEnd);
      
      // Notify React of the change
      const inputEvent = new Event('input', { bubbles: true });
      textarea.dispatchEvent(inputEvent);
      
      // Update the parent content if callback is available
      if (onContentChange) {
        onContentChange(redoState.content);
      }
      
      // Recalculate matches with new content
      const newCount = countMatchesInText(redoState.content);
      setTotalMatches(newCount);
      setCurrentMatchIndex(-1);
      
      return true;
    }
    
    return false;
  }, [textareaRef, onContentChange, countMatchesInText]);

  useEffect(() => {
    if (isOpen) {
      // Focus find input when opened
      setTimeout(() => {
        findInputRef.current?.focus();
      }, 100);
    }
  }, [isOpen]);

  useEffect(() => {
    if (!isOpen) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      } else if (e.key === 'Enter' && findText) {
        e.preventDefault();
        if (e.shiftKey) {
          // Call findPrevious logic directly
          if (!textareaRef?.current || !findText || totalMatches === 0) return;
          
          const textarea = textareaRef.current;
          const currentPos = textarea.selectionStart;
          
          const currentText = textarea.value;
          let prevIndex = findFromPosition(currentText, currentPos, 'backward');
          
          if (prevIndex === -1) {
            prevIndex = findFromPosition(currentText, currentText.length, 'backward');
          }
          
          if (prevIndex !== -1) {
            textarea.focus();
            textarea.setSelectionRange(prevIndex, prevIndex + findText.length);
            setCurrentMatchIndex(prevIndex);
            
            const rect = textarea.getBoundingClientRect();
            const selectionTop = (prevIndex / currentText.length) * textarea.scrollHeight;
            const targetScrollTop = Math.max(0, selectionTop - rect.height / 2);
            textarea.scrollTop = targetScrollTop;
          }
        } else {
          // Call findNext logic directly
          if (!textareaRef?.current || !findText || totalMatches === 0) return;
          
          const textarea = textareaRef.current;
          const currentPos = textarea.selectionEnd;
          
          const currentText = textarea.value;
          let nextIndex = findFromPosition(currentText, currentPos, 'forward');
          
          if (nextIndex === -1) {
            nextIndex = findFromPosition(currentText, 0, 'forward');
          }
          
          if (nextIndex !== -1) {
            textarea.focus();
            textarea.setSelectionRange(nextIndex, nextIndex + findText.length);
            setCurrentMatchIndex(nextIndex);
            
            const rect = textarea.getBoundingClientRect();
            const selectionTop = (nextIndex / currentText.length) * textarea.scrollHeight;
            const targetScrollTop = Math.max(0, selectionTop - rect.height / 2);
            textarea.scrollTop = targetScrollTop;
          }
        }
      } else if (e.metaKey && e.key === 'z' && textareaRef?.current) {
        // Use custom undo/redo system
        e.preventDefault();
        e.stopPropagation();
        
        const textarea = textareaRef.current;
        textarea.focus();
        
        if (e.shiftKey) {
          // Cmd+Shift+Z for redo
          performRedo();
        } else {
          // Cmd+Z for undo
          performUndo();
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, findText, onClose, textareaRef, content, totalMatches, findFromPosition, setCurrentMatchIndex, performUndo, performRedo]);

  // Count total matches using content prop (for initial counting)
  const countMatches = useCallback(() => {
    if (!findText || !content) {
      setTotalMatches(0);
      setCurrentMatchIndex(-1);
      return;
    }

    const count = countMatchesInText(content);
    setTotalMatches(count);
    if (count === 0) {
      setCurrentMatchIndex(-1);
    }
  }, [findText, content, countMatchesInText]);

  // Update match count when search parameters change
  useEffect(() => {
    countMatches();
  }, [countMatches]);

  const findNext = useCallback(() => {
    if (!textareaRef?.current || !findText || totalMatches === 0) return;

    const textarea = textareaRef.current;
    const currentText = textarea.value;
    const currentPos = textarea.selectionEnd;
    
    let nextIndex = findFromPosition(currentText, currentPos, 'forward');
    
    // If not found from current position, wrap to beginning
    if (nextIndex === -1) {
      nextIndex = findFromPosition(currentText, 0, 'forward');
    }
    
    if (nextIndex !== -1) {
      textarea.focus();
      textarea.setSelectionRange(nextIndex, nextIndex + findText.length);
      setCurrentMatchIndex(nextIndex);
      
      // Scroll the selection into view
      const rect = textarea.getBoundingClientRect();
      const selectionTop = (nextIndex / currentText.length) * textarea.scrollHeight;
      const targetScrollTop = Math.max(0, selectionTop - rect.height / 2);
      textarea.scrollTop = targetScrollTop;
    }
  }, [textareaRef, findText, totalMatches, findFromPosition]);

  const findPrevious = useCallback(() => {
    if (!textareaRef?.current || !findText || totalMatches === 0) return;

    const textarea = textareaRef.current;
    const currentText = textarea.value;
    const currentPos = textarea.selectionStart;
    
    let prevIndex = findFromPosition(currentText, currentPos, 'backward');
    
    // If not found before current position, wrap to end
    if (prevIndex === -1) {
      prevIndex = findFromPosition(currentText, currentText.length, 'backward');
    }
    
    if (prevIndex !== -1) {
      textarea.focus();
      textarea.setSelectionRange(prevIndex, prevIndex + findText.length);
      setCurrentMatchIndex(prevIndex);
      
      // Scroll the selection into view
      const rect = textarea.getBoundingClientRect();
      const selectionTop = (prevIndex / currentText.length) * textarea.scrollHeight;
      const targetScrollTop = Math.max(0, selectionTop - rect.height / 2);
      textarea.scrollTop = targetScrollTop;
    }
  }, [textareaRef, findText, totalMatches, findFromPosition]);


  const replace = useCallback(() => {
    if (!textareaRef?.current || !findText || totalMatches === 0) {
      return;
    }

    const textarea = textareaRef.current;
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = textarea.value.substring(start, end);
    
    // Check if current selection matches the find text (case sensitivity considered)
    const matchesSelected = caseSensitive ? 
      selectedText === findText : 
      selectedText.toLowerCase() === findText.toLowerCase();
    
    if (!matchesSelected || end - start !== findText.length) {
      // Current selection doesn't match, find next occurrence first
      findNext();
      return;
    }
    
    // Save current state for undo BEFORE making changes
    saveUndoState(textarea);
    
    // Make sure textarea has focus
    textarea.focus();
    
    // Perform the replacement
    const beforeValue = textarea.value;
    const newValue = beforeValue.substring(0, start) + replaceText + beforeValue.substring(end);
    
    // Update textarea value
    textarea.value = newValue;
    
    // Position cursor after the replacement
    const newCursorPos = start + replaceText.length;
    textarea.setSelectionRange(newCursorPos, newCursorPos);
    
    // Notify React of the change
    const inputEvent = new Event('input', { bubbles: true });
    textarea.dispatchEvent(inputEvent);
    
    // Update parent content if callback is available
    if (onContentChange) {
      onContentChange(newValue);
    }
    
    console.log('Replace completed with custom undo system');
    
    // Position cursor after the replaced text and find next using fresh textarea content
    setTimeout(() => {
      if (textareaRef.current) {
        const textarea = textareaRef.current;
        const newCursorPos = start + replaceText.length;
        textarea.setSelectionRange(newCursorPos, newCursorPos);
        setCurrentMatchIndex(-1);
        
        // Use the current textarea.value (which has the fresh content after replacement)
        const currentText = textarea.value;
        
        // Find next match from current position using fresh content
        let nextIndex = findFromPosition(currentText, newCursorPos, 'forward');
        
        // If not found from current position, wrap to beginning
        if (nextIndex === -1) {
          nextIndex = findFromPosition(currentText, 0, 'forward');
        }
        
        if (nextIndex !== -1) {
          textarea.focus();
          textarea.setSelectionRange(nextIndex, nextIndex + findText.length);
          setCurrentMatchIndex(nextIndex);
          
          // Scroll the selection into view using fresh content length
          const rect = textarea.getBoundingClientRect();
          const selectionTop = (nextIndex / currentText.length) * textarea.scrollHeight;
          const targetScrollTop = Math.max(0, selectionTop - rect.height / 2);
          textarea.scrollTop = targetScrollTop;
        }
        
        // Update total count using fresh content
        const newCount = countMatchesInText(currentText);
        setTotalMatches(newCount);
      }
    }, 10);
  }, [textareaRef, findText, totalMatches, caseSensitive, replaceText, findFromPosition, countMatchesInText, saveUndoState, onContentChange]);

  const replaceAll = useCallback(() => {
    if (!textareaRef?.current || !findText || !content || !onContentChange || totalMatches === 0) {
      return;
    }

    const textarea = textareaRef.current;
    
    // Save current state for undo BEFORE making changes
    saveUndoState(textarea);

    let text = content;
    let replacements = 0;
    
    if (wholeWord) {
      const regex = new RegExp(`\\b${escapeRegex(findText)}\\b`, caseSensitive ? 'g' : 'gi');
      const newText = text.replace(regex, () => {
        replacements++;
        return replaceText;
      });
      text = newText;
    } else {
      const searchText = caseSensitive ? findText : findText.toLowerCase();
      const targetText = caseSensitive ? text : text.toLowerCase();
      let index = 0;
      let newText = '';
      let lastIndex = 0;
      
      while ((index = targetText.indexOf(searchText, index)) !== -1) {
        newText += text.substring(lastIndex, index) + replaceText;
        lastIndex = index + findText.length;
        index = lastIndex;
        replacements++;
      }
      newText += text.substring(lastIndex);
      text = newText;
    }
    
    if (replacements > 0) {
      // Update textarea value
      textarea.value = text;
      textarea.setSelectionRange(0, 0); // Position cursor at start
      
      // Notify React of the change
      const inputEvent = new Event('input', { bubbles: true });
      textarea.dispatchEvent(inputEvent);
      
      // Update content using parent state
      onContentChange(text);
      
      // Reset state
      setCurrentMatchIndex(-1);
      setTotalMatches(0);
      
      console.log(`Replace All completed: ${replacements} replacements with custom undo system`);
      
      // After state update, recalculate match count
      setTimeout(() => {
        countMatches();
      }, 10);
    }
  }, [textareaRef, findText, content, onContentChange, totalMatches, replaceText, caseSensitive, wholeWord, escapeRegex, countMatches, saveUndoState]);

  // Drag functionality
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (!panelRef.current) return;
    
    setIsDragging(true);
    const rect = panelRef.current.getBoundingClientRect();
    setDragStart({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    });
    e.preventDefault();
  }, []);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging || !panelRef.current) return;
    
    const newX = e.clientX - dragStart.x;
    const newY = e.clientY - dragStart.y;
    
    // Keep panel within viewport bounds
    const maxX = window.innerWidth - panelRef.current.offsetWidth;
    const maxY = window.innerHeight - panelRef.current.offsetHeight;
    
    setPosition({
      x: Math.max(0, Math.min(newX, maxX)),
      y: Math.max(0, Math.min(newY, maxY))
    });
  }, [isDragging, dragStart]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  // Add/remove mouse event listeners for dragging
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.userSelect = 'none'; // Prevent text selection while dragging
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        document.body.style.userSelect = '';
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp]);

  // Reset position when panel opens
  useEffect(() => {
    if (isOpen) {
      setPosition({ x: 0, y: 60 });
    }
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div 
      ref={panelRef}
      className="find-replace-panel"
      style={{
        position: 'fixed',
        top: `${position.y}px`,
        right: position.x === 0 ? '16px' : 'auto',
        left: position.x > 0 ? `${position.x}px` : 'auto',
        width: '400px',
        background: 'var(--bg-primary)',
        border: '1px solid var(--border-color)',
        borderRadius: '8px',
        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)',
        zIndex: 100,
        overflow: 'hidden',
        cursor: isDragging ? 'grabbing' : 'default'
      }}
    >
      <div 
        className="find-replace-header"
        onMouseDown={handleMouseDown}
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: '8px 12px',
          background: 'var(--bg-secondary)',
          borderBottom: '1px solid var(--border-color)',
          cursor: isDragging ? 'grabbing' : 'grab',
          userSelect: 'none'
        }}
      >
        <span style={{ fontSize: '12px', fontWeight: '600', color: 'var(--text-primary)' }}>
          Find and Replace
          {undoStackRef.current.length > 0 && (
            <span style={{ 
              marginLeft: '8px', 
              fontSize: '10px', 
              color: 'var(--text-secondary)',
              opacity: 0.7 
            }}>
              ({undoStackRef.current.length} undo available)
            </span>
          )}
        </span>
        <button 
          onClick={onClose}
          style={{
            background: 'none',
            border: 'none',
            fontSize: '16px',
            cursor: 'pointer',
            color: 'var(--text-secondary)',
            padding: '0',
            width: '20px',
            height: '20px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        >
          ×
        </button>
      </div>
      
      <div className="find-replace-content" style={{ padding: '12px' }}>
        {/* Find row */}
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '8px' }}>
          <input
            ref={findInputRef}
            type="text"
            placeholder="Find..."
            value={findText}
            onChange={(e) => setFindText(e.target.value)}
            onKeyDown={(e) => {
              // Use custom undo/redo system for find input
              if (e.metaKey && (e.key === 'z' || e.key === 'Z')) {
                e.preventDefault();
                e.stopPropagation();
                
                if (textareaRef?.current) {
                  textareaRef.current.focus();
                  
                  // Use custom undo/redo system
                  if (e.shiftKey) {
                    // Redo (Cmd+Shift+Z)
                    performRedo();
                  } else {
                    // Undo (Cmd+Z)
                    performUndo();
                  }
                }
              }
            }}
            style={{
              flex: 1,
              padding: '6px 8px',
              fontSize: '12px',
              border: '1px solid var(--border-color)',
              borderRadius: '4px',
              background: 'var(--bg-primary)',
              color: 'var(--text-primary)'
            }}
          />
          <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
            <button 
              onClick={findPrevious}
              disabled={!findText || totalMatches === 0}
              style={{
                padding: '4px 6px',
                fontSize: '12px',
                border: '1px solid var(--border-color)',
                borderRadius: '3px',
                background: 'var(--bg-secondary)',
                color: 'var(--text-primary)',
                cursor: 'pointer'
              }}
            >
              ↑
            </button>
            <button 
              onClick={findNext}
              disabled={!findText || totalMatches === 0}
              style={{
                padding: '4px 6px',
                fontSize: '12px',
                border: '1px solid var(--border-color)',
                borderRadius: '3px',
                background: 'var(--bg-secondary)',
                color: 'var(--text-primary)',
                cursor: 'pointer'
              }}
            >
              ↓
            </button>
            <span style={{ fontSize: '11px', color: 'var(--text-secondary)', minWidth: '40px' }}>
              {totalMatches > 0 ? `${totalMatches} found` : '0 of 0'}
            </span>
          </div>
        </div>
        
        {/* Replace row */}
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '12px' }}>
          <input
            type="text"
            placeholder="Replace..."
            value={replaceText}
            onChange={(e) => setReplaceText(e.target.value)}
            onKeyDown={(e) => {
              // Use custom undo/redo system for replace input
              if (e.metaKey && (e.key === 'z' || e.key === 'Z')) {
                e.preventDefault();
                e.stopPropagation();
                
                if (textareaRef?.current) {
                  textareaRef.current.focus();
                  
                  // Use custom undo/redo system
                  if (e.shiftKey) {
                    // Redo (Cmd+Shift+Z)
                    performRedo();
                  } else {
                    // Undo (Cmd+Z)
                    performUndo();
                  }
                }
              }
            }}
            style={{
              flex: 1,
              padding: '6px 8px',
              fontSize: '12px',
              border: '1px solid var(--border-color)',
              borderRadius: '4px',
              background: 'var(--bg-primary)',
              color: 'var(--text-primary)'
            }}
          />
          <div style={{ display: 'flex', gap: '4px' }}>
            <button 
              onClick={replace}
              disabled={!findText || totalMatches === 0}
              style={{
                padding: '4px 8px',
                fontSize: '11px',
                border: '1px solid var(--border-color)',
                borderRadius: '3px',
                background: 'var(--bg-secondary)',
                color: 'var(--text-primary)',
                cursor: 'pointer'
              }}
            >
              Replace
            </button>
            <button 
              onClick={replaceAll}
              disabled={!findText || totalMatches === 0}
              style={{
                padding: '4px 8px',
                fontSize: '11px',
                border: '1px solid var(--border-color)',
                borderRadius: '3px',
                background: 'var(--bg-secondary)',
                color: 'var(--text-primary)',
                cursor: 'pointer'
              }}
            >
              All
            </button>
          </div>
        </div>
        
        {/* Options */}
        <div style={{ display: 'flex', gap: '12px' }}>
          <label style={{ display: 'flex', alignItems: 'center', gap: '4px', fontSize: '11px', color: 'var(--text-secondary)' }}>
            <input 
              type="checkbox" 
              checked={caseSensitive}
              onChange={(e) => setCaseSensitive(e.target.checked)}
              style={{ margin: 0 }}
            />
            Case sensitive
          </label>
          <label style={{ display: 'flex', alignItems: 'center', gap: '4px', fontSize: '11px', color: 'var(--text-secondary)' }}>
            <input 
              type="checkbox" 
              checked={wholeWord}
              onChange={(e) => setWholeWord(e.target.checked)}
              style={{ margin: 0 }}
            />
            Whole word
          </label>
        </div>
      </div>
    </div>
  );
}