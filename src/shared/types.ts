// Shared types for the Nexus application

export interface Note {
  id: string;
  content: string;
  title?: string;
  created_at: string;
  updated_at: string;
  parent_id?: string | null;  // null for root notes
  is_folder?: boolean;        // true for folders, false for notes
  is_expanded?: boolean;      // for folder collapse/expand state
  is_pinned?: boolean;       // for pinned notes
  is_temporary?: boolean;    // for cleanup if user cancels
  deleted_at?: string | null; // for trash functionality
}

// Tree node interface for UI components
export interface NoteNode {
  id: string;
  title: string | null;
  content: string;
  parentId: string | null;
  children: NoteNode[];
  isFolder: boolean;
  isExpanded: boolean;
  isPinned: boolean;
  isTemporary?: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Search result with path information
export interface SearchResult {
  id: string;
  title: string | null;
  content: string;
  snippet?: string;
  path: string[];
  matchType: 'title' | 'content';
}

export interface Folder {
  id: string;
  name: string;
  parent_id?: string;
}

export interface ClipboardItem {
  id: string;
  content: string;
  type: 'text' | 'image' | 'file';
  created_at: string;
}

export interface AppState {
  currentView: 'notes' | 'translator' | 'calculator' | 'clipboard';
  selectedNote?: Note;
  searchQuery: string;
}

// Shortcut configuration types
export interface ShortcutConfig {
  id: string;
  name: string;
  description: string;
  defaultShortcut: string;
  currentShortcut: string;
  action: string;
  category: 'window' | 'navigation' | 'actions';
}

export interface ShortcutSettings {
  shortcuts: Record<string, string>; // action -> shortcut mapping
}

// Electron API types
export interface ElectronAPI {
  // Notes operations
  createNote: (content: string, title?: string, parentId?: string, isTemporary?: boolean) => Promise<string>;
  createFolder: (name: string, parentId?: string, isTemporary?: boolean) => Promise<string>;
  updateNote: (id: string, content: string, title?: string) => Promise<void>;
  deleteNote: (id: string, deleteChildren?: boolean) => Promise<void>;
  moveNote: (noteId: string, newParentId: string | null) => Promise<void>;
  toggleFolder: (folderId: string) => Promise<void>;
  searchNotes: (query: string) => Promise<SearchResult[]>;
  getAllNotes: () => Promise<Note[]>;
  getNoteById: (noteId: string) => Promise<Note | null>;
  getNotesTree: () => Promise<NoteNode[]>;
  getNotePath: (noteId: string) => Promise<string[]>;
  
  // Clipboard operations
  getClipboardHistory: () => Promise<ClipboardItem[]>;
  clearClipboardHistory: () => Promise<void>;
  copyToClipboard: (content: string) => Promise<void>;
  removeClipboardItem: (id: string) => Promise<void>;
  searchClipboardHistory: (query: string) => Promise<ClipboardItem[]>;
  
  // Trash operations
  getTrashedNotes: () => Promise<Note[]>;
  restoreNote: (noteId: string) => Promise<void>;
  permanentlyDeleteNote: (noteId: string) => Promise<void>;
  emptyTrash: () => Promise<number>;
  
  // Window operations
  hideWindow: () => void;
  showWindow: () => void;
  toggleWindow: () => void;
  
  // App operations
  quit: () => void;
  
  // Shortcut operations
  getShortcutSettings: () => Promise<ShortcutSettings>;
  updateShortcutSettings: (settings: ShortcutSettings) => Promise<void>;
  getAvailableShortcuts: () => Promise<ShortcutConfig[]>;
  testShortcut: (shortcut: string, action?: string) => Promise<boolean>;
}

// Electron Events interface
export interface ElectronEvents {
  onCreateNewNote: (callback: () => void) => void;
  onFocusSearch: (callback: () => void) => void;
  onSwitchToCalculator: (callback: () => void) => void;
  onSwitchToClipboard: (callback: () => void) => void;
  onShowPreferences: (callback: () => void) => void;
  onShowShortcutSettings: (callback: () => void) => void;
  onToggleSidebar: (callback: () => void) => void;
  onSwitchToTrash: (callback: () => void) => void;
  onNoteUpdated: (callback: (note: Note) => void) => void;
  onNoteCreated: (callback: (note: Note) => void) => void;
  onNoteDeleted: (callback: (noteId: string) => void) => void;
  
  // Generic event listeners for shortcuts and other events
  on: (channel: string, callback: (...args: any[]) => void) => void;
  removeListener: (channel: string, callback: (...args: any[]) => void) => void;
  removeAllListeners: (channel: string) => void;
}

declare global {
  interface Window {
    electronAPI: ElectronAPI;
    electronEvents: ElectronEvents;
  }
}
