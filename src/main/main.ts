import { app, BrowserWindow } from 'electron';
import { join } from 'path';
import * as fs from 'fs';
import * as path from 'path';
import { SimpleTray } from './tray';
import { registerShortcuts, initializeShortcuts } from './shortcuts';
import { setupIpcHandlers } from './ipc';
import { NotesDB } from '../database/notes';
import { ClipboardManager } from './clipboard';
import { SafeLogger } from '../utils/logger';
import { errorHandler, ErrorHandler } from './errorHandler';

class NexusApp {
  private mainWindow: BrowserWindow | null = null;
  private tray: SimpleTray | null = null;
  private notesDB: NotesDB | null = null;
  private clipboardManager: ClipboardManager | null = null;
  private isQuitting: boolean = false;

  constructor() {
    this.setupApp();
  }

  private setupApp() {
    console.log('setupApp called');

    // Ensure single instance
    const gotTheLock = app.requestSingleInstanceLock();
    console.log('Got lock:', gotTheLock);

    if (!gotTheLock) {
      console.log('Another instance is running, quitting');
      app.quit();
      return;
    }

    // Handle second instance attempt
    app.on('second-instance', () => {
      // Someone tried to run a second instance, focus our window instead
      if (this.mainWindow) {
        if (this.mainWindow.isMinimized()) {
          this.mainWindow.restore();
        }
        this.mainWindow.show();
        this.mainWindow.focus();
      }
    });

    // Handle app ready
    app.whenReady().then(() => {
      console.log('App ready event fired');
      try {
        console.log('About to create window');
        this.createWindow();
        console.log('Window created successfully');
        this.setupTray();
        this.setupShortcuts();
        this.setupDatabase();
        this.setupClipboard();
        this.setupIPC();
        console.log('App initialization completed');
      } catch (error) {
        console.error('Error during app initialization:', error);
      }
    });

    // Handle window closed
    app.on('window-all-closed', () => {
      // On macOS, keep app running even when all windows are closed
      if (process.platform !== 'darwin') {
        app.quit();
      }
    });

    // Handle app activation (macOS) - improved dock behavior
    app.on('activate', () => {
      if (BrowserWindow.getAllWindows().length === 0) {
        this.createWindow();
      } else if (this.mainWindow) {
        // If window exists but is hidden, show it
        if (!this.mainWindow.isVisible()) {
          this.mainWindow.show();
        }
        // Focus the window
        this.mainWindow.focus();
        // Restore from minimized state if needed
        if (this.mainWindow.isMinimized()) {
          this.mainWindow.restore();
        }
      }
    });

    // Handle before quit
    app.on('before-quit', () => {
      SafeLogger.info('App is quitting...');
      this.isQuitting = true;
      this.cleanup();
    });

    // Handle process termination signals gracefully
    process.on('SIGINT', () => {
      SafeLogger.info('Received SIGINT, shutting down gracefully...');
      app.quit();
    });

    process.on('SIGTERM', () => {
      SafeLogger.info('Received SIGTERM, shutting down gracefully...');
      app.quit();
    });

    // Handle uncaught exceptions to prevent crashes
    process.on('uncaughtException', (error) => {
      SafeLogger.error('Uncaught exception:', error);

      // Check if this is an EIO error and handle gracefully
      if (error.message && error.message.includes('EIO')) {
        SafeLogger.error('EIO error detected - likely due to process termination or file system issues');
        this.cleanup();
        // Don't exit immediately for EIO errors, they're often recoverable
        return;
      }

      // For other critical errors, show dialog and exit gracefully
      if (this.mainWindow && !this.mainWindow.isDestroyed()) {
        this.mainWindow.webContents.executeJavaScript(`
          alert('A critical error occurred: ${error.message}. The application will restart.');
        `).catch(() => {
          // Ignore if webContents is not ready
        }).finally(() => {
          app.relaunch();
          app.exit(1);
        });
      } else {
        app.relaunch();
        app.exit(1);
      }
    });

    process.on('unhandledRejection', (reason, promise) => {
      SafeLogger.error('Unhandled rejection at:', promise, 'reason:', reason);

      // Check if this is an EIO-related rejection
      const reasonStr = String(reason);
      if (reasonStr.includes('EIO') || reasonStr.includes('write')) {
        SafeLogger.error('EIO-related promise rejection detected');
        this.cleanup();
        return;
      }

      // Don't exit immediately for promise rejections, just log them
    });

  }

  private async createWindow() {
    // Prevent creating multiple windows
    if (this.mainWindow) {
      this.mainWindow.show();
      this.mainWindow.focus();
      return;
    }

    this.mainWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      minWidth: 600,
      minHeight: 400,
      titleBarStyle: 'default', // Show native traffic lights
      trafficLightPosition: { x: 20, y: 20 }, // Standard macOS positioning
      backgroundColor: '#ffffff', // White background
      show: false, // Don't show until ready
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: join(__dirname, '../preload/preload.js'),
        webSecurity: false, // Disable for localhost in development
        devTools: false, // Disable DevTools in production
      },
    });
    
    // DevTools disabled for production build

    // Set the main window for error handling
    errorHandler.setMainWindow(this.mainWindow);

    // Enable standard window controls
    this.mainWindow.setWindowButtonVisibility(true);

    // Load the React app
    if (process.env.NODE_ENV === 'development') {
      const devServerUrl = this.getDevServerUrl();
      SafeLogger.info(`Loading development URL: ${devServerUrl}`);

      // Validate development server is running before loading
      const isDevServerRunning = await this.checkDevServer();
      if (!isDevServerRunning) {
        const error = new Error(`Development server is not running on ${devServerUrl}`);
        errorHandler.handleError(error, ErrorHandler.CONTEXTS.DEV_SERVER_NOT_RUNNING);
        return;
      }

      try {
        await this.mainWindow.loadURL(devServerUrl);
        // Production build - DevTools disabled
        // Force show window immediately for debugging
        this.mainWindow.show();
        this.mainWindow.focus();
        this.mainWindow.moveTop();
      } catch (error) {
        const loadError = error instanceof Error ? error : new Error('Failed to load development URL');
        errorHandler.handleError(loadError, {
          ...ErrorHandler.CONTEXTS.LOAD_FAILED,
          details: 'Failed to load the development server. The server may be starting up or experiencing issues.'
        });
        return;
      }
    } else {
      try {
        await this.mainWindow.loadFile(join(__dirname, '../../renderer/index.html'));
      } catch (error) {
        const loadError = error instanceof Error ? error : new Error('Failed to load production build');
        errorHandler.handleError(loadError, {
          ...ErrorHandler.CONTEXTS.LOAD_FAILED,
          details: 'Failed to load the production build. The build files may be missing or corrupted.'
        });
        return;
      }
    }

    // Handle window events
    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
    });

    // Handle window close - hide window instead of closing on macOS (except when quitting)
    this.mainWindow.on('close', (event) => {
      if (process.platform === 'darwin' && !this.isQuitting) {
        event.preventDefault();
        this.mainWindow?.hide();
      }
    });

    // Handle renderer process crashes and failures
    this.mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL) => {
      const error = new Error(`Failed to load ${validatedURL}: ${errorCode} - ${errorDescription}`);

      if (process.env.NODE_ENV === 'development' && validatedURL.includes('localhost:')) {
        errorHandler.handleError(error, ErrorHandler.CONTEXTS.DEV_SERVER_NOT_RUNNING);
      } else {
        errorHandler.handleError(error, {
          ...ErrorHandler.CONTEXTS.LOAD_FAILED,
          code: `LOAD_${errorCode}`,
          details: `Failed to load ${validatedURL}\nError: ${errorDescription}`
        });
      }
    });

    this.mainWindow.webContents.on('render-process-gone', (event, details) => {
      const error = new Error(`Renderer process crashed: ${details.reason}`);
      errorHandler.handleError(error, {
        type: 'runtime',
        severity: 'critical',
        code: 'RENDERER_CRASH',
        details: `Reason: ${details.reason}\nExit Code: ${details.exitCode}`,
        suggestions: [
          'The application will restart automatically',
          'If this keeps happening, try restarting your computer',
          'Check for available system updates'
        ],
        canRetry: true,
        canRecover: true
      });

      // Restart the window
      setTimeout(() => {
        this.createWindow();
      }, 1000);
    });

    // Handle content loading
    this.mainWindow.webContents.on('did-finish-load', () => {
      SafeLogger.info('Renderer content loaded');
      // Show and focus the window after content loads
      this.mainWindow?.show();
      this.mainWindow?.focus();
      this.mainWindow?.moveTop();
      
      // Check if this is first launch and show helpful notification
      this.checkFirstLaunch();
    });

    this.mainWindow.webContents.on('did-fail-load', (_event, errorCode, errorDescription, validatedURL) => {
      SafeLogger.error('Failed to load renderer:', errorCode, errorDescription, validatedURL);
    });

    // Window is already shown, just log when it's ready
    this.mainWindow.once('ready-to-show', () => {
      SafeLogger.info('Window ready to show');
    });
  }

  private getDevServerUrl(): string {
    try {
      // Try to read the URL discovered by the validation script
      // __dirname in compiled code points to dist/main/main, so we need to go up to project root
      const urlPath = join(__dirname, '../../../.dev-server-url');
      SafeLogger.info(`Looking for dev server URL at: ${urlPath}`);
      
      if (fs.existsSync(urlPath)) {
        const url = fs.readFileSync(urlPath, 'utf8').trim();
        SafeLogger.info(`Found dev server URL: ${url}`);
        if (url && url.startsWith('http://localhost:')) {
          return url;
        }
      } else {
        SafeLogger.warn(`Dev server URL file not found at: ${urlPath}`);
      }
    } catch (error) {
      SafeLogger.warn('Could not read dev server URL file:', error);
    }
    
    // Fallback to default URL
    SafeLogger.info('Using fallback URL: http://localhost:5173');
    return 'http://localhost:5173';
  }

  private async checkDevServer(): Promise<boolean> {
    const devServerUrl = this.getDevServerUrl();
    try {
      const response = await fetch(devServerUrl, {
        method: 'HEAD',
        signal: AbortSignal.timeout(3000), // 3 second timeout
      });
      return response.ok;
    } catch (error) {
      SafeLogger.warn(`Development server check failed for ${devServerUrl}:`, error);
      return false;
    }
  }

  private showDevServerError() {
    const { dialog } = require('electron');

    const result = dialog.showMessageBoxSync(this.mainWindow || null, {
      type: 'error',
      title: 'Development Server Not Running',
      message: 'The Vite development server is not running',
      detail: 'To start the application properly, please run:\n\nnpm run dev\n\nThis will start both the development server and Electron.',
      buttons: ['Quit', 'Retry', 'Show Instructions'],
      defaultId: 2,
      cancelId: 0,
    });

    switch (result) {
      case 0: // Quit
        app.quit();
        break;
      case 1: // Retry
        setTimeout(() => {
          this.createWindow();
        }, 1000);
        break;
      case 2: // Show Instructions
        this.showDevelopmentInstructions();
        break;
    }
  }

  private showDevelopmentInstructions() {
    const { dialog, shell } = require('electron');

    const result = dialog.showMessageBoxSync(this.mainWindow || null, {
      type: 'info',
      title: 'Development Setup Instructions',
      message: 'How to start the Nexus development environment',
      detail: 'Follow these steps to start the application:\n\n' +
              '1. Open Terminal\n' +
              '2. Navigate to the project directory\n' +
              '3. Run: npm run dev\n\n' +
              'This command will:\n' +
              '• Start the Vite development server (React frontend)\n' +
              '• Compile TypeScript in watch mode\n' +
              '• Launch Electron automatically\n\n' +
              'Alternative: You can also run each component separately:\n' +
              '• npm run dev:vite (starts frontend server)\n' +
              '• npm run dev:main (compiles backend)\n' +
              '• npm run dev:electron (starts Electron)',
      buttons: ['Quit', 'Retry', 'Open Terminal'],
      defaultId: 1,
      cancelId: 0,
    });

    switch (result) {
      case 0: // Quit
        app.quit();
        break;
      case 1: // Retry
        setTimeout(() => {
          this.createWindow();
        }, 1000);
        break;
      case 2: // Open Terminal
        // Open Terminal app on macOS
        shell.openExternal('file:///System/Applications/Utilities/Terminal.app');
        app.quit();
        break;
    }
  }

  private showLoadError(context: string) {
    const { dialog } = require('electron');

    const result = dialog.showMessageBoxSync(this.mainWindow || null, {
      type: 'error',
      title: 'Failed to Load Application',
      message: `Failed to load ${context}`,
      detail: context === 'development server'
        ? 'The development server appears to be running but failed to load the application.\n\n' +
          'This could be due to:\n' +
          '• Network connectivity issues\n' +
          '• Development server startup problems\n' +
          '• Port conflicts\n\n' +
          'Try restarting the development environment with: npm run dev'
        : 'The production build failed to load.\n\n' +
          'This could be due to:\n' +
          '• Missing build files\n' +
          '• Corrupted installation\n' +
          '• File permission issues\n\n' +
          'Try rebuilding the application with: npm run build',
      buttons: ['Quit', 'Retry'],
      defaultId: 1,
      cancelId: 0,
    });

    switch (result) {
      case 0: // Quit
        app.quit();
        break;
      case 1: // Retry
        setTimeout(() => {
          this.createWindow();
        }, 2000);
        break;
    }
  }

  private setupTray() {
    if (this.mainWindow) {
      this.tray = new SimpleTray(this.mainWindow);
    }
  }

  private setupShortcuts() {
    // Initialize shortcut settings first
    initializeShortcuts();
    
    if (this.mainWindow) {
      // Wait for window to be ready before registering shortcuts
      this.mainWindow.once('ready-to-show', () => {
        console.log('Registering shortcuts after window ready');
        registerShortcuts(this.mainWindow!);
        
      });
      
      // Also register immediately in case window is already ready
      if (this.mainWindow.webContents.isLoading() === false) {
        console.log('Window already ready, registering shortcuts immediately');
        registerShortcuts(this.mainWindow);
      }
    }
  }

  private checkFirstLaunch() {
    const { app, Notification } = require('electron');
    const fs = require('fs');
    const path = require('path');
    
    try {
      const userDataPath = app.getPath('userData');
      const firstLaunchFile = path.join(userDataPath, '.nexus-first-launch');
      
      // Check if this is the first launch
      if (!fs.existsSync(firstLaunchFile)) {
        // Create the file to mark that we've shown the first launch info
        fs.writeFileSync(firstLaunchFile, new Date().toISOString());
        
        // Show system notification with helpful tip
        if (Notification.isSupported()) {
          setTimeout(() => {
            const notification = new Notification({
              title: '🎉 欢迎使用 Nexus！',
              body: '小贴士：使用 ⌘⇧Space 可以随时快速打开 Nexus\n即使关闭窗口，应用仍在后台运行',
              silent: false,
              timeoutType: 'default'
            });
            
            notification.show();
            
            // Auto close after 5 seconds
            setTimeout(() => {
              notification.close();
            }, 5000);
            
          }, 2000); // Delay to not overwhelm user on first launch
        }
        
        SafeLogger.info('First launch detected, showing welcome notification');
      }
    } catch (error) {
      SafeLogger.error('Error checking first launch:', error);
    }
  }

  private setupDatabase() {
    try {
      const dbPath = join(app.getPath('userData'), 'nexus.db');
      console.log(`Setting up database at: ${dbPath}`);
      this.notesDB = new NotesDB(dbPath);

      // Clean up any temporary items from previous sessions
      console.log('About to clean up temporary items...');
      try {
        const beforeCount = this.notesDB.getTemporaryItemsCount();
        console.log(`Found ${beforeCount} temporary items before cleanup`);

        const cleanedCount = this.notesDB.cleanupTemporaryItems();
        console.log(`Cleanup completed. Removed ${cleanedCount} temporary items.`);

        const afterCount = this.notesDB.getTemporaryItemsCount();
        console.log(`Found ${afterCount} temporary items after cleanup`);
      } catch (cleanupError) {
        console.error('Error during cleanup, but continuing:', cleanupError);
      }

      console.log('Database setup completed successfully');
    } catch (error) {
      const dbError = error instanceof Error ? error : new Error('Database initialization failed');
      errorHandler.handleError(dbError, {
        ...ErrorHandler.CONTEXTS.DATABASE_ERROR,
        details: 'Failed to initialize the SQLite database. The application may not function properly.',
        suggestions: [
          'Restart the application',
          'Check if you have sufficient disk space',
          'Ensure the application has write permissions to the user data directory',
          'Try clearing the application data and restarting'
        ]
      });

      // Don't throw here to prevent app crash, but log the error
      // The app will continue but database operations will fail gracefully
    }
  }

  private setupClipboard() {
    this.clipboardManager = new ClipboardManager();
    this.clipboardManager.start();
  }

  private setupIPC() {
    if (this.notesDB && this.clipboardManager && this.mainWindow) {
      setupIpcHandlers(this.notesDB, this.clipboardManager, this.mainWindow);
    }
  }

  public getMainWindow(): BrowserWindow | null {
    return this.mainWindow;
  }

  public getNotesDB(): NotesDB | null {
    return this.notesDB;
  }

  public getClipboardManager(): ClipboardManager | null {
    return this.clipboardManager;
  }


  private cleanup(): void {
    SafeLogger.info('Starting application cleanup...');

    try {
      // Stop clipboard manager
      if (this.clipboardManager) {
        SafeLogger.info('Stopping clipboard manager...');
        this.clipboardManager.stop();
        this.clipboardManager = null;
      }
    } catch (error) {
      SafeLogger.error('Error stopping clipboard manager:', error);
    }

    try {
      // Destroy tray
      if (this.tray) {
        SafeLogger.info('Destroying tray...');
        this.tray.destroy();
        this.tray = null;
      }
    } catch (error) {
      SafeLogger.error('Error destroying tray:', error);
    }

    try {
      // Close database
      if (this.notesDB) {
        SafeLogger.info('Closing database...');
        this.notesDB.close();
        this.notesDB = null;
      }
    } catch (error) {
      SafeLogger.error('Error closing database:', error);
    }

    try {
      // Flush any pending console output
      SafeLogger.flush();
    } catch (error) {
      // Ignore flush errors
    }

    SafeLogger.info('Application cleanup completed');
  }
}

// Create the app instance only if we're running in Electron
let nexusApp: NexusApp | null = null;

// Check if we're running in Electron context
if (typeof process !== 'undefined' && process.versions && process.versions.electron) {
  nexusApp = new NexusApp();
}

export default nexusApp;
