import { ipc<PERSON>ain, BrowserWindow, globalShortcut } from 'electron';
import { NotesDB } from '../database/notes';
import { ClipboardManager } from './clipboard';
import { SafeLogger } from '../utils/logger';
import { getShortcutSettings, updateShortcuts } from './shortcuts';
import { ShortcutSettings } from '../shared/types';

export function setupIpcHandlers(
  notesDB: NotesDB,
  clipboardManager: ClipboardManager,
  mainWindow: BrowserWindow
) {
  // Notes operations
  ipcMain.handle('notes:create', async (_event, content: string, title?: string, parentId?: string, isTemporary?: boolean) => {
    try {
      // If no title provided, generate a smart name
      let finalTitle = title;
      if (!finalTitle) {
        finalTitle = await notesDB.generateSmartNoteName(parentId || null);
      }
      
      const noteId = await notesDB.createNote(content, finalTitle, parentId, isTemporary);
      return noteId;
    } catch (error) {
      SafeLogger.error('Error creating note:', error);
      throw error;
    }
  });

  ipcMain.handle('notes:createFolder', async (_event, name: string, parentId?: string, isTemporary?: boolean) => {
    try {
      const folderId = await notesDB.createFolder(name, parentId, isTemporary);
      return folderId;
    } catch (error) {
      SafeLogger.error('Error creating folder:', error);
      throw error;
    }
  });

  ipcMain.handle('notes:update', async (_event, id: string, content: string, title?: string) => {
    try {
      await notesDB.updateNote(id, content, title);
    } catch (error) {
      SafeLogger.error('Error updating note:', error);
      throw error;
    }
  });

  ipcMain.handle('notes:delete', async (_event, id: string) => {
    try {
      await notesDB.deleteNote(id);
    } catch (error) {
      SafeLogger.error('Error deleting note:', error);
      throw error;
    }
  });

  ipcMain.handle('notes:move', async (_event, noteId: string, newParentId: string | null, newSortOrder?: number) => {
    try {
      await notesDB.moveNote(noteId, newParentId, newSortOrder);
    } catch (error) {
      SafeLogger.error('Error moving note:', error);
      throw error;
    }
  });

  ipcMain.handle('notes:search', async (_event, query: string) => {
    try {
      const notes = await notesDB.searchNotes(query);
      return notes;
    } catch (error) {
      SafeLogger.error('Error searching notes:', error);
      throw error;
    }
  });

  ipcMain.handle('notes:getAll', async (_event) => {
    try {
      const notes = await notesDB.getAllNotes();
      return notes;
    } catch (error) {
      SafeLogger.error('Error getting all notes:', error);
      throw error;
    }
  });

  ipcMain.handle('notes:getById', async (_event, noteId: string) => {
    try {
      const note = await notesDB.getNoteById(noteId);
      return note;
    } catch (error) {
      SafeLogger.error('Error getting note by id:', error);
      throw error;
    }
  });

  ipcMain.handle('notes:getTree', async (_event) => {
    try {
      const tree = await notesDB.getNotesTree();
      return tree;
    } catch (error) {
      SafeLogger.error('Error getting notes tree:', error);
      throw error;
    }
  });

  ipcMain.handle('notes:toggleFolder', async (_event, folderId: string) => {
    try {
      await notesDB.toggleFolder(folderId);
    } catch (error) {
      SafeLogger.error('Error toggling folder:', error);
      throw error;
    }
  });

  ipcMain.handle('notes:getPath', async (_event, noteId: string) => {
    try {
      const path = await notesDB.getNotePath(noteId);
      return path;
    } catch (error) {
      SafeLogger.error('Error getting note path:', error);
      throw error;
    }
  });

  // Trash operations
  ipcMain.handle('trash:getNotes', async (_event) => {
    try {
      const trashedNotes = await notesDB.getTrashedNotes();
      return trashedNotes;
    } catch (error) {
      SafeLogger.error('Error getting trashed notes:', error);
      throw error;
    }
  });

  ipcMain.handle('trash:restore', async (_event, noteId: string) => {
    try {
      await notesDB.restoreNote(noteId);
    } catch (error) {
      SafeLogger.error('Error restoring note:', error);
      throw error;
    }
  });

  ipcMain.handle('trash:permanentDelete', async (_event, noteId: string) => {
    try {
      await notesDB.permanentlyDeleteNote(noteId);
    } catch (error) {
      SafeLogger.error('Error permanently deleting note:', error);
      throw error;
    }
  });

  ipcMain.handle('trash:empty', async (_event) => {
    try {
      const deletedCount = await notesDB.emptyTrash();
      return deletedCount;
    } catch (error) {
      SafeLogger.error('Error emptying trash:', error);
      throw error;
    }
  });

  // Clipboard operations
  ipcMain.handle('clipboard:getHistory', async (_event) => {
    try {
      return clipboardManager.getHistory();
    } catch (error) {
      SafeLogger.error('Error getting clipboard history:', error);
      throw error;
    }
  });

  ipcMain.handle('clipboard:clear', async (_event) => {
    try {
      clipboardManager.clearHistory();
    } catch (error) {
      SafeLogger.error('Error clearing clipboard history:', error);
      throw error;
    }
  });

  ipcMain.handle('clipboard:copy', async (_event, content: string) => {
    try {
      clipboardManager.copyToClipboard(content);
    } catch (error) {
      SafeLogger.error('Error copying to clipboard:', error);
      throw error;
    }
  });

  ipcMain.handle('clipboard:remove', async (_event, id: string) => {
    try {
      clipboardManager.removeItem(id);
    } catch (error) {
      SafeLogger.error('Error removing clipboard item:', error);
      throw error;
    }
  });

  ipcMain.handle('clipboard:search', async (_event, query: string) => {
    try {
      return clipboardManager.searchHistory(query);
    } catch (error) {
      SafeLogger.error('Error searching clipboard history:', error);
      throw error;
    }
  });

  // Window operations
  ipcMain.handle('window:hide', async (_event) => {
    mainWindow.hide();
  });

  ipcMain.handle('window:show', async (_event) => {
    mainWindow.show();
    mainWindow.focus();
  });

  ipcMain.handle('window:toggle', async (_event) => {
    if (mainWindow.isVisible() && mainWindow.isFocused()) {
      mainWindow.hide();
    } else {
      mainWindow.show();
      mainWindow.focus();
    }
  });

  // App operations
  ipcMain.handle('app:quit', async (_event) => {
    require('electron').app.quit();
  });

  // Shortcut operations
  ipcMain.handle('shortcuts:getSettings', async (_event) => {
    try {
      const manager = getShortcutSettings();
      return manager.getSettings();
    } catch (error) {
      SafeLogger.error('Error getting shortcut settings:', error);
      throw error;
    }
  });

  ipcMain.handle('shortcuts:updateSettings', async (_event, settings: ShortcutSettings) => {
    try {
      const manager = getShortcutSettings();
      manager.updateSettings(settings);
      // Re-register shortcuts with new configuration
      updateShortcuts(mainWindow);
    } catch (error) {
      SafeLogger.error('Error updating shortcut settings:', error);
      throw error;
    }
  });

  ipcMain.handle('shortcuts:getAvailable', async (_event) => {
    try {
      const manager = getShortcutSettings();
      return manager.getAvailableShortcuts();
    } catch (error) {
      SafeLogger.error('Error getting available shortcuts:', error);
      throw error;
    }
  });

  ipcMain.handle('shortcuts:test', async (_event, shortcut: string, action?: string) => {
    try {
      // For local shortcuts (like create-new-note), we don't need to test global availability
      const localShortcuts = ['create-new-note', 'create-note'];
      
      if (action && localShortcuts.includes(action)) {
        // Local shortcuts don't need global registration, so they're always "available"
        return true;
      }
      
      // For global shortcuts, test if they're available by trying to register temporarily
      const success = globalShortcut.register(shortcut, () => {});
      if (success) {
        globalShortcut.unregister(shortcut);
      }
      return success;
    } catch (error) {
      SafeLogger.error('Error testing shortcut:', error);
      return false;
    }
  });

  // Set up database change listeners for real-time updates
  notesDB.on('noteUpdated', (note) => {
    mainWindow.webContents.send('note-updated', note);
  });

  notesDB.on('noteCreated', (note) => {
    mainWindow.webContents.send('note-created', note);
  });

  notesDB.on('noteDeleted', (noteId) => {
    mainWindow.webContents.send('note-deleted', noteId);
  });

  SafeLogger.info('IPC handlers setup complete');
}
